# OpenAppSec Documentation

Welcome to the comprehensive documentation for OpenAppSec, a machine learning-powered web application and API security platform.

## 📚 Documentation Overview

This documentation provides detailed information about OpenAppSec's architecture, deployment options, development guidelines, and operational procedures.

### 📋 Table of Contents

1. [Architecture Documentation](#architecture-documentation)
2. [System Diagrams](#system-diagrams)
3. [Developer Resources](#developer-resources)
4. [Deployment Guides](#deployment-guides)
5. [Quick Start](#quick-start)
6. [Additional Resources](#additional-resources)

## 🏗️ Architecture Documentation

### [📖 Architecture Overview](ARCHITECTURE.md)

Comprehensive documentation covering:
- **System Architecture**: High-level component overview and relationships
- **Core Components**: Detailed breakdown of infrastructure and security components
- **Security Applications**: WAAP, access control, rate limiting, and IPS systems
- **Machine Learning Pipeline**: Supervised and unsupervised learning models
- **Configuration Management**: Multi-source configuration and validation
- **Development Guide**: Building, testing, and contributing to the project

## 📊 System Diagrams

Our documentation includes detailed Mermaid diagrams that visualize the system architecture and data flows:

### System Architecture Diagram
- **High-level system overview** showing all major components
- **Component relationships** and communication patterns
- **External integrations** with web servers and management systems
- **Color-coded component categories** for easy understanding

### Component Interaction Diagrams
- **Sequence diagrams** showing HTTP request processing flow
- **Security analysis chain** with decision points
- **Configuration and health monitoring** workflows
- **Parallel processing** of security components

### Machine Learning Pipeline
- **Feature engineering** and data preprocessing
- **Model training and deployment** workflows
- **Real-time inference** and decision making
- **Continuous learning** and model updates

### Deployment Architecture Diagrams
- **Kubernetes deployment** with ingress controllers and pods
- **Docker containerized** deployment with networks and volumes
- **Linux native** deployment with system services

### Data Flow Diagrams
- **End-to-end request processing** with numbered steps
- **High-performance IPC** through shared memory
- **Security analysis pipeline** with parallel processing
- **Monitoring and logging** data flows

## 👨‍💻 Developer Resources

### [🛠️ Developer Guide](DEVELOPER_GUIDE.md)

Complete guide for developers including:
- **Development Environment Setup**: Prerequisites and installation
- **Building the Project**: CMake configuration and build targets
- **Component Development**: Creating new components and following patterns
- **Testing Strategy**: Unit tests, integration tests, and performance testing
- **Debugging and Troubleshooting**: Logging, GDB, Valgrind, and core dumps
- **Contributing Guidelines**: Code review process and standards
- **Code Style and Standards**: Formatting, documentation, and best practices

### Component Development Framework

OpenAppSec uses a sophisticated component-based architecture:

```cpp
// Example component structure
class MyComponent
    : public Component,
      Singleton::Provide<I_MyComponent>,
      Singleton::Consume<I_Logging>
{
public:
    void init() override;
    void preload() override;
    void fini() override;
    
    // Interface implementation
    bool processRequest(const std::string& data) override;
};
```

## 🚀 Deployment Guides

### Kubernetes Deployment

OpenAppSec integrates seamlessly with Kubernetes:

```bash
# Install using Helm
helm repo add openappsec https://charts.openappsec.io
helm install openappsec openappsec/openappsec

# Install using installer
wget https://downloads.openappsec.io/open-appsec-k8s-install
chmod +x open-appsec-k8s-install
./open-appsec-k8s-install
```

### Docker Deployment

Multi-container deployment with Docker Compose:

```yaml
version: '3.8'
services:
  openappsec-orchestration:
    image: openappsec/orchestration:latest
    volumes:
      - config:/etc/cp/conf
      - data:/etc/cp/data
      - logs:/var/log/nano_agent
    
  openappsec-http-handler:
    image: openappsec/http-handler:latest
    depends_on:
      - openappsec-orchestration
    volumes:
      - shared-memory:/dev/shm
```

### Linux Native Deployment

System service installation:

```bash
# Download and install
wget https://downloads.openappsec.io/open-appsec-install
chmod +x open-appsec-install
./open-appsec-install --auto

# Manual installation
install-cp-nano-agent.sh --install --hybrid_mode
install-cp-nano-service-http-transaction-handler.sh --install
install-cp-nano-attachment-registration-manager.sh --install
```

## ⚡ Quick Start

### 1. Choose Your Deployment Method

- **Kubernetes**: Best for cloud-native applications
- **Docker**: Ideal for development and testing
- **Linux Native**: Perfect for traditional server deployments

### 2. Install OpenAppSec

Follow the installation instructions for your chosen deployment method above.

### 3. Configure Security Policies

```yaml
# Example security policy
apiVersion: v1
kind: ConfigMap
metadata:
  name: openappsec-policy
data:
  policy.yaml: |
    security:
      waap:
        enabled: true
        mode: "prevent"
        ml_model: "advanced"
      rate_limiting:
        enabled: true
        requests_per_minute: 1000
      access_control:
        enabled: true
        default_action: "allow"
```

### 4. Monitor and Manage

- **Web UI**: Access the OpenAppSec management interface
- **Prometheus Metrics**: Monitor performance and security metrics
- **Grafana Dashboards**: Visualize security events and system health
- **Log Analysis**: Review security decisions and system events

## 🔧 Configuration Examples

### Basic WAAP Configuration

```yaml
waap:
  enabled: true
  mode: "prevent"  # or "monitor"
  confidence_threshold: 0.8
  ml_models:
    supervised: "global-v2.1"
    unsupervised: "environment-specific"
  learning_mode:
    enabled: true
    duration: "7d"
```

### Rate Limiting Configuration

```yaml
rate_limiting:
  enabled: true
  global_limit: 10000  # requests per minute
  per_ip_limit: 100
  per_user_limit: 200
  burst_allowance: 50
  adaptive_thresholds: true
```

### Access Control Configuration

```yaml
access_control:
  enabled: true
  default_action: "allow"
  rules:
    - path: "/admin/*"
      method: "*"
      action: "deny"
      except_ips: ["***********/24"]
    - path: "/api/v1/*"
      method: "POST"
      rate_limit: 50
```

## 📈 Monitoring and Observability

### Key Metrics

- **Request Rate**: Requests per second processed
- **Security Events**: Blocked requests and threat detections
- **ML Model Performance**: Accuracy and false positive rates
- **System Performance**: CPU, memory, and latency metrics

### Alerting

Configure alerts for:
- High attack detection rates
- System performance degradation
- Configuration changes
- Component health issues

## 🔗 Additional Resources

### Official Links
- [Project Website](https://openappsec.io)
- [Official Documentation](https://docs.openappsec.io/)
- [Video Tutorials](https://www.openappsec.io/tutorials)
- [GitHub Repository](https://github.com/openappsec/openappsec)

### Community Resources
- [Contributing Guidelines](../CONTRIBUTING.md)
- [Code of Conduct](../CODE_OF_CONDUCT.md)
- [Security Policy](../SECURITY.md)
- [License Information](../LICENSE)

### Related Repositories
- [openappsec/attachment](https://github.com/openappsec/attachment) - Web server integration
- [openappsec/smartsync](https://github.com/openappsec/smartsync) - Learning data correlation
- [openappsec/smartsync-shared-files](https://github.com/openappsec/smartsync-shared-files) - Storage interface

## 🤝 Contributing

We welcome contributions from the community! Please see our [Contributing Guidelines](../CONTRIBUTING.md) for details on:

- Code contribution process
- Bug reporting procedures
- Feature request guidelines
- Documentation improvements

## 📄 License

OpenAppSec is open source and available under the Apache 2.0 license. See [LICENSE](../LICENSE) for full details.

---

**Need Help?** 
- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/openappsec/openappsec/issues)
- 💬 Discussions: [GitHub Discussions](https://github.com/openappsec/openappsec/discussions)

*Last updated: July 2025*
