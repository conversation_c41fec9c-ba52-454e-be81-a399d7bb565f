# OpenAppSec Developer Guide

## Table of Contents
1. [Getting Started](#getting-started)
2. [Development Environment Setup](#development-environment-setup)
3. [Building the Project](#building-the-project)
4. [Component Development](#component-development)
5. [Testing Strategy](#testing-strategy)
6. [Debugging and Troubleshooting](#debugging-and-troubleshooting)
7. [Contributing Guidelines](#contributing-guidelines)
8. [Code Style and Standards](#code-style-and-standards)

## Getting Started

OpenAppSec is a C++ project with a modular, component-based architecture. This guide will help you set up your development environment and understand the codebase structure.

### Prerequisites

Before you begin development, ensure you have the following tools and dependencies installed:

#### Required Tools
- **C++ Compiler**: GCC 8+ or Clang 10+
- **CMake**: Version 3.16 or higher
- **Git**: For version control
- **Make**: Build automation tool

#### Required Libraries
- **Boost**: C++ libraries (development headers)
- **OpenSSL**: Cryptographic library
- **PCRE2**: Perl Compatible Regular Expressions
- **libxml2**: XML parsing library
- **GTest**: Google Test framework
- **GMock**: Google Mock framework
- **cURL**: HTTP client library
- **Redis**: In-memory data structure store
- **Hiredis**: Redis C client library
- **MaxmindDB**: IP geolocation database library
- **yq**: YAML processor

#### Optional Tools
- **Docker**: For containerized development
- **Kubernetes**: For testing K8s deployments
- **Valgrind**: Memory debugging tool
- **GDB**: GNU Debugger
- **Clang-format**: Code formatting tool

## Development Environment Setup

### Ubuntu/Debian Setup

```bash
# Update package manager
sudo apt update

# Install build tools
sudo apt install -y build-essential cmake git make

# Install required libraries
sudo apt install -y \
    libboost-all-dev \
    libssl-dev \
    libpcre2-dev \
    libxml2-dev \
    libgtest-dev \
    libgmock-dev \
    libcurl4-openssl-dev \
    redis-server \
    libhiredis-dev \
    libmaxminddb-dev

# Install yq
sudo wget -qO /usr/local/bin/yq https://github.com/mikefarah/yq/releases/latest/download/yq_linux_amd64
sudo chmod +x /usr/local/bin/yq
```

### Alpine Linux Setup

```bash
# Update package manager
apk update

# Install dependencies
apk add boost-dev openssl-dev pcre2-dev libxml2-dev \
        gtest-dev curl-dev hiredis-dev redis libmaxminddb-dev yq \
        build-base cmake git make
```

### macOS Setup

```bash
# Install Homebrew if not already installed
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install dependencies
brew install boost openssl pcre2 libxml2 googletest curl redis hiredis libmaxminddb yq cmake
```

## Building the Project

### Clone the Repository

```bash
git clone https://github.com/openappsec/openappsec.git
cd openappsec/
```

### Configure Build

```bash
# Create build directory
mkdir -p build
cd build

# Configure with CMake
cmake -DCMAKE_INSTALL_PREFIX=../build_out ..

# Alternative: Configure for debug build
cmake -DCMAKE_BUILD_TYPE=Debug -DCMAKE_INSTALL_PREFIX=../build_out ..
```

### Build and Install

```bash
# Build the project
make -j$(nproc)

# Install to build_out directory
make install

# Create distribution packages
make package
```

### Docker Build

```bash
# Build Docker image
make docker

# This creates a local image called 'agent-docker'
```

### Build Targets

The project provides several build targets:

- `make all`: Build all components
- `make install`: Install built components
- `make package`: Create distribution packages
- `make docker`: Build Docker image
- `make test`: Run unit tests
- `make clean`: Clean build artifacts

## Component Development

### Component Architecture

OpenAppSec follows a component-based architecture where each component:

1. **Implements a specific interface**: Defined in `core/include/services_sdk/interfaces/`
2. **Follows the singleton pattern**: Uses dependency injection for inter-component communication
3. **Has a standardized lifecycle**: Init, preload, run, shutdown phases
4. **Provides configuration support**: YAML/JSON configuration files

### Component Structure

Each component typically has the following structure:

```
components/my_component/
├── CMakeLists.txt              # Build configuration
├── my_component.cc             # Implementation
├── include/
│   ├── my_component.h          # Public header
│   └── i_my_component.h        # Interface definition
├── my_component_ut/            # Unit tests
│   ├── CMakeLists.txt
│   └── my_component_ut.cc
└── README.md                   # Component documentation
```

### Creating a New Component

1. **Define the Interface**

Create the interface header in `components/include/`:

```cpp
// i_my_component.h
#ifndef __I_MY_COMPONENT_H__
#define __I_MY_COMPONENT_H__

class I_MyComponent
{
public:
    virtual ~I_MyComponent() = default;
    virtual bool processRequest(const std::string& data) = 0;
    virtual void configure(const std::string& config) = 0;
};

#endif // __I_MY_COMPONENT_H__
```

2. **Implement the Component**

Create the component implementation:

```cpp
// my_component.h
#ifndef __MY_COMPONENT_H__
#define __MY_COMPONENT_H__

#include "component.h"
#include "singleton.h"
#include "i_my_component.h"
#include "i_logging.h"

class MyComponent
    : public Component,
      Singleton::Provide<I_MyComponent>,
      Singleton::Consume<I_Logging>
{
public:
    MyComponent();
    ~MyComponent();

    void init() override;
    void preload() override;
    void fini() override;

    // I_MyComponent implementation
    bool processRequest(const std::string& data) override;
    void configure(const std::string& config) override;

private:
    class Impl;
    std::unique_ptr<Impl> pimpl;
};

#endif // __MY_COMPONENT_H__
```

3. **Add CMake Configuration**

```cmake
# CMakeLists.txt
add_library(my_component my_component.cc)

target_link_libraries(my_component
    ngen_core
    ${COMMON_LIBRARIES}
)

# Add unit tests
add_subdirectory(my_component_ut)
```

### Component Registration

Register your component in the appropriate node's main.cc:

```cpp
#include "my_component.h"

int main(int argc, char **argv)
{
    NodeComponents<
        // ... other components
        MyComponent
    > comps;

    return comps.run("My Service", argc, argv);
}
```

## Testing Strategy

### Unit Testing

OpenAppSec uses Google Test (GTest) and Google Mock (GMock) for unit testing.

#### Writing Unit Tests

```cpp
// my_component_ut.cc
#include "my_component.h"
#include "cptest.h"
#include "mock/mock_logging.h"

using namespace std;
using namespace testing;

class MyComponentTest : public Test
{
public:
    MyComponentTest()
    {
        // Setup mocks
        EXPECT_CALL(mock_logging, init()).WillOnce(Return());
    }

    StrictMock<MockLogging> mock_logging;
    MyComponent component;
};

TEST_F(MyComponentTest, ProcessValidRequest)
{
    // Arrange
    string test_data = "valid_request_data";
    
    // Act
    bool result = component.processRequest(test_data);
    
    // Assert
    EXPECT_TRUE(result);
}

TEST_F(MyComponentTest, ProcessInvalidRequest)
{
    // Arrange
    string test_data = "invalid_request_data";
    
    // Act
    bool result = component.processRequest(test_data);
    
    // Assert
    EXPECT_FALSE(result);
}
```

#### Running Unit Tests

```bash
# Run all tests
make test

# Run specific test
./build/components/my_component/my_component_ut/my_component_ut

# Run tests with verbose output
./build/components/my_component/my_component_ut/my_component_ut --gtest_verbose
```

### Integration Testing

Integration tests verify component interactions and end-to-end functionality.

#### Example Integration Test

```cpp
// integration_test.cc
#include "cptest.h"
#include "http_manager.h"
#include "waap.h"

class IntegrationTest : public Test
{
public:
    void SetUp() override
    {
        // Initialize components
        http_manager.init();
        waap.init();
    }

    HttpManager http_manager;
    WaapComponent waap;
};

TEST_F(IntegrationTest, EndToEndRequestProcessing)
{
    // Test complete request processing pipeline
    // ...
}
```

### Performance Testing

Use performance tests to ensure components meet latency and throughput requirements:

```cpp
TEST_F(MyComponentTest, PerformanceTest)
{
    auto start = chrono::high_resolution_clock::now();
    
    // Perform operations
    for (int i = 0; i < 10000; ++i) {
        component.processRequest("test_data");
    }
    
    auto end = chrono::high_resolution_clock::now();
    auto duration = chrono::duration_cast<chrono::microseconds>(end - start);
    
    // Assert performance requirements
    EXPECT_LT(duration.count(), 1000000); // Less than 1 second
}
```

## Debugging and Troubleshooting

### Logging

OpenAppSec provides a comprehensive logging framework:

```cpp
#include "i_logging.h"

// Get logging instance
auto logging = Singleton::Consume<I_Logging>::by<MyComponent>();

// Log messages at different levels
logging->debug("Debug message: {}", variable);
logging->info("Info message: {}", variable);
logging->warning("Warning message: {}", variable);
logging->error("Error message: {}", variable);
```

### Debug Builds

Build with debug symbols for better debugging:

```bash
cmake -DCMAKE_BUILD_TYPE=Debug -DCMAKE_INSTALL_PREFIX=../build_out ..
make -j$(nproc)
```

### Using GDB

```bash
# Run component with GDB
gdb ./build/nodes/http_transaction_handler/http_transaction_handler

# Set breakpoints
(gdb) break MyComponent::processRequest
(gdb) run

# Examine variables
(gdb) print variable_name
(gdb) backtrace
```

### Memory Debugging with Valgrind

```bash
# Check for memory leaks
valgrind --leak-check=full --show-leak-kinds=all ./my_component

# Check for memory errors
valgrind --tool=memcheck ./my_component
```

### Core Dump Analysis

```bash
# Enable core dumps
ulimit -c unlimited

# Analyze core dump
gdb ./my_component core.12345

# Get stack trace
(gdb) bt
(gdb) info registers
```

## Contributing Guidelines

### Code Review Process

1. **Fork the repository** and create a feature branch
2. **Make your changes** following coding standards
3. **Write comprehensive tests** for new functionality
4. **Update documentation** as needed
5. **Submit a pull request** with detailed description

### Commit Message Format

```
type(scope): brief description

Detailed explanation of the change, including:
- What was changed
- Why it was changed
- Any breaking changes

Fixes #issue_number
```

Types: `feat`, `fix`, `docs`, `style`, `refactor`, `test`, `chore`

### Branch Naming

- Feature branches: `feature/component-name-description`
- Bug fixes: `fix/issue-description`
- Documentation: `docs/documentation-update`

## Code Style and Standards

### C++ Coding Standards

- **Follow Google C++ Style Guide** with project-specific modifications
- **Use RAII** for resource management
- **Prefer smart pointers** over raw pointers
- **Use const correctness** throughout
- **Follow naming conventions**:
  - Classes: `PascalCase`
  - Functions: `camelCase`
  - Variables: `snake_case`
  - Constants: `UPPER_SNAKE_CASE`

### Code Formatting

Use clang-format for consistent code formatting:

```bash
# Format single file
clang-format -i my_component.cc

# Format all source files
find . -name "*.cc" -o -name "*.h" | xargs clang-format -i
```

### Documentation Standards

- **Document all public APIs** using Doxygen comments
- **Provide component README** files
- **Include usage examples** in documentation
- **Keep documentation up-to-date** with code changes

### Example Doxygen Documentation

```cpp
/**
 * @brief Processes an HTTP request for security analysis
 * 
 * This function analyzes the incoming HTTP request using machine learning
 * models and security policies to determine if the request should be allowed
 * or blocked.
 * 
 * @param request The HTTP request to analyze
 * @param context The request context information
 * @return SecurityDecision The decision (ALLOW, BLOCK, or MONITOR)
 * 
 * @throws SecurityException If analysis fails
 * 
 * @example
 * ```cpp
 * HttpRequest request = parseRequest(raw_data);
 * RequestContext context = buildContext(request);
 * SecurityDecision decision = analyzeRequest(request, context);
 * ```
 */
SecurityDecision analyzeRequest(const HttpRequest& request, 
                               const RequestContext& context);
```

---

This developer guide provides the foundation for contributing to OpenAppSec. For specific component documentation, refer to the README files in each component directory.
