# OpenAppSec Architecture Documentation

## Table of Contents
1. [Overview](#overview)
2. [System Architecture](#system-architecture)
3. [Core Components](#core-components)
4. [Security Applications](#security-applications)
5. [Deployment Architectures](#deployment-architectures)
6. [Data Flow](#data-flow)
7. [Machine Learning Pipeline](#machine-learning-pipeline)
8. [Configuration Management](#configuration-management)
9. [Development Guide](#development-guide)

## Overview

OpenAppSec is a machine learning-powered web application and API security platform that provides preemptive threat protection against OWASP Top-10 and zero-day attacks. The system is designed with a modular architecture that can be deployed as an add-on to various web servers and API gateways including NGINX, Kubernetes Ingress, Kong, and Envoy.

### Key Features
- **Machine Learning-Based Protection**: Uses both supervised and unsupervised ML models
- **Real-time Threat Detection**: Analyzes HTTP requests in real-time
- **Multi-deployment Support**: Kubernetes, Docker, Linux native deployments
- **Modular Architecture**: Component-based design for flexibility and scalability
- **Zero-day Protection**: Detects unknown attack patterns through behavioral analysis

## System Architecture

The OpenAppSec system follows a distributed, component-based architecture with clear separation of concerns:

### High-Level Architecture Components

1. **Core Infrastructure** (`core/`): Provides fundamental services and utilities
2. **Security Applications** (`components/security_apps/`): Implements security logic and policies
3. **Attachment Intakers** (`components/attachment-intakers/`): Interfaces with web servers
4. **Orchestration Services** (`nodes/orchestration/`): Manages system lifecycle and configuration
5. **HTTP Transaction Handlers** (`nodes/http_transaction_handler/`): Processes HTTP traffic
6. **Utilities and Libraries** (`components/utils/`): Shared functionality across components

### Component Communication

Components communicate through:
- **Shared Memory IPC**: High-performance inter-process communication
- **REST APIs**: Configuration and management interfaces
- **Message Queues**: Asynchronous event processing
- **Socket Communication**: Real-time data exchange

## Core Components

### Infrastructure Components (`core/`)

The core infrastructure provides essential services that all other components depend on:

#### Essential Services
- **MainLoop**: Event-driven execution framework
- **Messaging**: Inter-component communication system
- **Configuration**: Centralized configuration management
- **Logging**: Structured logging and audit trails
- **REST Server**: HTTP API endpoints
- **Environment**: System environment abstraction
- **Time Proxy**: Time management and synchronization
- **Encryptor**: Cryptographic services
- **Intelligence Service**: ML model management and inference

#### Utility Services
- **Buffer Management**: Memory pool management
- **Socket Interface**: Network communication abstraction
- **Shell Command**: System command execution
- **Agent Details**: System information collection
- **Tenant Management**: Multi-tenancy support
- **Instance Awareness**: Cluster coordination

### Security Applications (`components/security_apps/`)

Security applications implement the core protection logic:

#### Web Application and API Protection (WAAP)
- **Request Analysis**: Deep inspection of HTTP requests
- **ML Model Integration**: Real-time threat scoring
- **Policy Enforcement**: Block/allow decisions based on analysis
- **Learning Mode**: Behavioral pattern learning

#### Layer 7 Access Control
- **URL-based Access Control**: Path and method restrictions
- **User-based Policies**: Identity-driven access decisions
- **Geographic Filtering**: Location-based access control

#### Rate Limiting
- **Request Rate Control**: Prevents abuse and DoS attacks
- **Adaptive Thresholds**: Dynamic rate adjustment based on behavior
- **Granular Controls**: Per-user, per-IP, per-endpoint limits

#### Intrusion Prevention System (IPS)
- **Signature-based Detection**: Known attack pattern matching
- **Behavioral Analysis**: Anomaly detection
- **Protocol Validation**: HTTP protocol compliance checking

### Attachment System

The attachment system provides the interface between web servers and the OpenAppSec security engine:

#### NGINX Attachment
- **Module Integration**: Native NGINX module for request interception
- **Shared Memory Communication**: High-performance data exchange
- **Configuration Synchronization**: Dynamic policy updates

#### Registration Manager
- **Service Discovery**: Automatic attachment registration
- **Health Monitoring**: Attachment status tracking
- **Load Balancing**: Request distribution across instances

## Security Applications

### WAAP (Web Application and API Protection)

The WAAP component is the core security engine that provides ML-based threat protection:

#### Request Processing Pipeline
1. **Request Interception**: Captures HTTP requests from attachments
2. **Content Extraction**: Parses headers, body, parameters
3. **Feature Engineering**: Extracts ML features from request data
4. **ML Inference**: Applies supervised and unsupervised models
5. **Decision Making**: Determines block/allow action
6. **Response Generation**: Returns decision to attachment

#### Machine Learning Models
- **Supervised Model**: Pre-trained on global attack patterns
- **Unsupervised Model**: Environment-specific behavioral learning
- **Feature Extraction**: Automated feature engineering from HTTP data
- **Model Updates**: Continuous learning and model refresh

### Orchestration System

The orchestration system manages the lifecycle and configuration of all components:

#### Service Management
- **Service Controller**: Manages service lifecycle (start/stop/restart)
- **Health Monitoring**: Continuous health checking of all services
- **Dependency Management**: Ensures proper service startup order
- **Resource Management**: CPU and memory monitoring

#### Configuration Management
- **Manifest Processing**: Handles configuration manifests
- **Policy Distribution**: Distributes security policies to components
- **Dynamic Updates**: Hot-reload of configuration changes
- **Version Control**: Configuration versioning and rollback

#### Package Management
- **Software Updates**: Manages component updates and patches
- **Dependency Resolution**: Handles component dependencies
- **Rollback Capability**: Safe rollback of failed updates

## Deployment Architectures

OpenAppSec supports multiple deployment scenarios to fit different infrastructure requirements:

### Kubernetes Deployment

#### NGINX Ingress Integration
- **Ingress Controller**: Integrates with NGINX Ingress Controller
- **Pod Sidecar**: Runs as sidecar container alongside applications
- **ConfigMap Management**: Configuration through Kubernetes ConfigMaps
- **Service Mesh**: Optional integration with service mesh technologies

#### Kong Integration
- **Plugin Architecture**: Deploys as Kong plugin
- **API Gateway**: Leverages Kong's API management capabilities
- **Kubernetes Native**: Full Kubernetes resource management

### Docker Deployment

#### Container Architecture
- **Multi-container Setup**: Separate containers for different components
- **Shared Volumes**: Configuration and data persistence
- **Network Isolation**: Secure inter-container communication
- **Orchestration**: Docker Compose for multi-service deployment

### Linux Native Deployment

#### System Integration
- **Systemd Services**: Native Linux service management
- **Package Management**: RPM/DEB package distribution
- **File System Integration**: Standard Linux directory structure
- **Process Management**: Native process supervision

## Data Flow

### HTTP Request Processing Flow

The following describes how HTTP requests flow through the OpenAppSec system:

1. **Request Interception**: Web server attachment intercepts HTTP request
2. **Shared Memory Transfer**: Request data transferred via shared memory to HTTP Transaction Handler
3. **Request Parsing**: HTTP Transaction Handler parses and normalizes request data
4. **Security Analysis**: Request sent to WAAP component for ML analysis
5. **Policy Evaluation**: Additional security policies (rate limiting, access control) applied
6. **Decision Aggregation**: All security decisions combined into final verdict
7. **Response Generation**: Decision sent back to attachment
8. **Request Forwarding**: If allowed, request forwarded to backend application
9. **Response Processing**: Application response optionally processed for additional security
10. **Logging and Metrics**: All actions logged and metrics updated

### Configuration Flow

1. **Configuration Source**: Configuration from files, Kubernetes, or management UI
2. **Orchestration Processing**: Orchestration service validates and processes configuration
3. **Policy Distribution**: Policies distributed to relevant security components
4. **Component Updates**: Components update their runtime configuration
5. **Validation**: Configuration changes validated through health checks

### Learning Flow

1. **Traffic Observation**: WAAP observes legitimate traffic patterns
2. **Feature Extraction**: ML features extracted from benign requests
3. **Model Training**: Unsupervised model updated with new patterns
4. **Model Validation**: New model validated against known good traffic
5. **Model Deployment**: Updated model deployed to production
6. **Performance Monitoring**: Model performance continuously monitored

## Machine Learning Pipeline

### Supervised Learning Model

The supervised model is pre-trained on millions of requests and provides baseline security:

#### Training Data
- **Global Attack Patterns**: Known attack signatures from worldwide sources
- **Benign Traffic**: Legitimate request patterns across various applications
- **Feature Engineering**: Automated extraction of security-relevant features
- **Label Quality**: High-quality labeling of malicious vs. benign requests

#### Model Architecture
- **Ensemble Methods**: Multiple ML algorithms combined for better accuracy
- **Feature Selection**: Automated selection of most predictive features
- **Regularization**: Prevents overfitting to training data
- **Performance Optimization**: Optimized for low-latency inference

### Unsupervised Learning Model

The unsupervised model learns application-specific patterns:

#### Learning Process
- **Baseline Establishment**: Initial learning period to establish normal patterns
- **Anomaly Detection**: Identifies requests that deviate from learned patterns
- **Adaptive Thresholds**: Dynamic adjustment of anomaly thresholds
- **Continuous Learning**: Ongoing model updates with new traffic patterns

#### Feature Engineering
- **URL Patterns**: Learns normal URL structures and parameters
- **User Behavior**: Models typical user interaction patterns
- **Temporal Patterns**: Understands time-based access patterns
- **Content Analysis**: Analyzes request content for anomalies

## Configuration Management

### Configuration Sources

OpenAppSec supports multiple configuration sources:

#### File-based Configuration
- **YAML/JSON Files**: Human-readable configuration files
- **Environment Variables**: Runtime configuration overrides
- **Command Line Arguments**: Service-specific parameters

#### Kubernetes Configuration
- **ConfigMaps**: Kubernetes-native configuration management
- **Secrets**: Secure storage of sensitive configuration
- **Custom Resources**: OpenAppSec-specific Kubernetes resources
- **Annotations**: Service-level configuration through annotations

#### Management UI Configuration
- **Web Interface**: Graphical configuration management
- **API Integration**: RESTful API for programmatic configuration
- **Policy Templates**: Pre-defined security policy templates
- **Multi-tenant Support**: Tenant-specific configuration isolation

### Configuration Validation

All configuration changes go through validation:

1. **Schema Validation**: Ensures configuration matches expected schema
2. **Dependency Checking**: Validates component dependencies
3. **Security Validation**: Checks for security policy conflicts
4. **Performance Impact**: Estimates performance impact of changes
5. **Rollback Planning**: Prepares rollback strategy for changes

## Development Guide

### Building the Project

#### Prerequisites
- **C++ Compiler**: GCC 8+ or Clang 10+
- **CMake**: Version 3.16 or higher
- **Dependencies**: Boost, OpenSSL, PCRE2, libxml2, GTest, GMock, cURL, Redis, Hiredis, MaxmindDB

#### Build Process
```bash
# Clone repository
git clone https://github.com/openappsec/openappsec.git
cd openappsec/

# Configure build
cmake -DCMAKE_INSTALL_PREFIX=build_out .

# Build and install
make install
make package

# Create Docker image
make docker
```

### Component Development

#### Component Structure
Each component follows a standard structure:
- **Interface Definition**: Pure virtual interface class
- **Implementation**: Concrete implementation class
- **Configuration**: Component-specific configuration schema
- **Unit Tests**: Comprehensive test coverage
- **Integration Tests**: Component interaction tests

#### Component Lifecycle
1. **Initialization**: Component registration and dependency injection
2. **Configuration**: Loading and validation of component configuration
3. **Startup**: Component initialization and resource allocation
4. **Runtime**: Normal operation and request processing
5. **Shutdown**: Graceful shutdown and resource cleanup

### Testing Strategy

#### Unit Testing
- **Component Isolation**: Each component tested in isolation
- **Mock Dependencies**: Mock objects for external dependencies
- **Coverage Requirements**: Minimum 80% code coverage
- **Automated Execution**: Tests run on every code change

#### Integration Testing
- **Component Interaction**: Tests component communication
- **End-to-end Scenarios**: Full request processing pipeline tests
- **Performance Testing**: Load and stress testing
- **Security Testing**: Penetration testing and vulnerability scanning

### Debugging and Monitoring

#### Logging Framework
- **Structured Logging**: JSON-formatted log messages
- **Log Levels**: Configurable logging levels (DEBUG, INFO, WARN, ERROR)
- **Component Tagging**: Each log message tagged with component identifier
- **Performance Logging**: Request processing time tracking

#### Metrics and Monitoring
- **Prometheus Integration**: Metrics exported in Prometheus format
- **Health Checks**: Component health status endpoints
- **Performance Metrics**: Request rate, response time, error rate
- **Security Metrics**: Attack detection rate, false positive rate

#### Debugging Tools
- **Core Dumps**: Automatic core dump generation on crashes
- **Debug Symbols**: Debug information included in development builds
- **Memory Profiling**: Built-in memory leak detection
- **Performance Profiling**: CPU and memory usage profiling

---

*This documentation provides a comprehensive overview of the OpenAppSec architecture. For specific implementation details, refer to the source code and component-specific documentation in the respective directories.*
