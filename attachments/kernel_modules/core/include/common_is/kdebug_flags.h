// Copyright (C) 2022 Check Point Software Technologies Ltd. All rights reserved.

// Licensed under the Apache License, Version 2.0 (the "License");
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifdef DEFINE_KDEBUG_FLAG

DEFINE_KDEBUG_FLAG(kernelStartup)
DEFINE_KDEBUG_FLAG(l4Firewall)
DEFINE_KDEBUG_FLAG(ftpHandler)
DEFINE_KDEBUG_FLAG(geneve)
DEFINE_KDEBUG_FLAG(l4Ips)
DEFINE_KDEBUG_FLAG(tableIs)
DEFINE_KDEBUG_FLAG(logIs)
DEFINE_KDEBUG_FLAG(debugIs)
DEFINE_KDEBUG_FLAG(ioctlInfra)
DEFINE_KDEBUG_FLAG(trapInfra)
DEFINE_KDEBUG_FLAG(netfilterAttachment)
DEFINE_KDEBUG_FLAG(accessControlPolicy)
DEFINE_KDEBUG_FLAG(connection)
DEFINE_KDEBUG_FLAG(assetResolver)
DEFINE_KDEBUG_FLAG(statefulValidation)
DEFINE_KDEBUG_FLAG(statelessValidation)
DEFINE_KDEBUG_FLAG(kernelMetric)
DEFINE_KDEBUG_FLAG(tproxy)
DEFINE_KDEBUG_FLAG(tenantStats)
DEFINE_KDEBUG_FLAG(uuidTranslation)
DEFINE_KDEBUG_FLAG(antibotResolver)

#endif // DEFINE_KDEBUG_FLAG
