/**
 * Copyright 2019-present GraphQL Foundation
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
/** @generated */
#define FOR_EACH_CONCRETE_TYPE(MACRO) \
MACRO(Document, document) \
MACRO(OperationDefinition, operation_definition) \
MACRO(VariableDefinition, variable_definition) \
MACRO(SelectionSet, selection_set) \
MACRO(Field, field) \
MACRO(Argument, argument) \
MACRO(FragmentSpread, fragment_spread) \
MACRO(InlineFragment, inline_fragment) \
MACRO(FragmentDefinition, fragment_definition) \
MACRO(Variable, variable) \
MACRO(IntValue, int_value) \
MACRO(FloatValue, float_value) \
MACRO(StringValue, string_value) \
MACRO(BooleanValue, boolean_value) \
MACRO(NullValue, null_value) \
MACRO(EnumValue, enum_value) \
MACRO(ListValue, list_value) \
MACRO(ObjectValue, object_value) \
MACRO(ObjectField, object_field) \
MACRO(Directive, directive) \
MACRO(NamedType, named_type) \
MACRO(ListType, list_type) \
MACRO(NonNullType, non_null_type) \
MACRO(Name, name) \
MACRO(SchemaDefinition, schema_definition) \
MACRO(OperationTypeDefinition, operation_type_definition) \
MACRO(ScalarTypeDefinition, scalar_type_definition) \
MACRO(ObjectTypeDefinition, object_type_definition) \
MACRO(FieldDefinition, field_definition) \
MACRO(InputValueDefinition, input_value_definition) \
MACRO(InterfaceTypeDefinition, interface_type_definition) \
MACRO(UnionTypeDefinition, union_type_definition) \
MACRO(EnumTypeDefinition, enum_type_definition) \
MACRO(EnumValueDefinition, enum_value_definition) \
MACRO(InputObjectTypeDefinition, input_object_type_definition) \
MACRO(TypeExtensionDefinition, type_extension_definition) \
MACRO(DirectiveDefinition, directive_definition)
