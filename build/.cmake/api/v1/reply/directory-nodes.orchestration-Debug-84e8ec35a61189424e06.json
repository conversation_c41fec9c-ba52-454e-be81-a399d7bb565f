{"backtraceGraph": {"commands": ["install"], "files": ["nodes/orchestration/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 40, "parent": 0}, {"command": 0, "file": 0, "line": 41, "parent": 0}, {"command": 0, "file": 0, "line": 43, "parent": 0}, {"command": 0, "file": 0, "line": 44, "parent": 0}, {"command": 0, "file": 0, "line": 45, "parent": 0}, {"command": 0, "file": 0, "line": 46, "parent": 0}, {"command": 0, "file": 0, "line": 47, "parent": 0}, {"command": 0, "file": 0, "line": 53, "parent": 0}, {"command": 0, "file": 0, "line": 59, "parent": 0}, {"command": 0, "file": 0, "line": 65, "parent": 0}, {"command": 0, "file": 0, "line": 71, "parent": 0}, {"command": 0, "file": 0, "line": 77, "parent": 0}, {"command": 0, "file": 0, "line": 83, "parent": 0}, {"command": 0, "file": 0, "line": 89, "parent": 0}, {"command": 0, "file": 0, "line": 95, "parent": 0}, {"command": 0, "file": 0, "line": 101, "parent": 0}, {"command": 0, "file": 0, "line": 117, "parent": 0}, {"command": 0, "file": 0, "line": 133, "parent": 0}, {"command": 0, "file": 0, "line": 139, "parent": 0}, {"command": 0, "file": 0, "line": 149, "parent": 0}, {"command": 0, "file": 0, "line": 155, "parent": 0}, {"command": 0, "file": 0, "line": 164, "parent": 0}]}, "installers": [{"backtrace": 1, "component": "Unspecified", "destination": "bin", "paths": ["nodes/orchestration/orchestration_comp"], "targetId": "orchestration_comp::@b474fdd263f216d3bed2", "targetIndex": 97, "type": "target"}, {"backtrace": 1, "component": "Unspecified", "cxxModuleBmiTarget": {"id": "orchestration_comp::@b474fdd263f216d3bed2", "index": 97}, "destination": "bin", "type": "cxxModuleBmi"}, {"backtrace": 2, "component": "Unspecified", "destination": "orchestration/bin", "paths": ["nodes/orchestration/orchestration_comp"], "targetId": "orchestration_comp::@b474fdd263f216d3bed2", "targetIndex": 97, "type": "target"}, {"backtrace": 2, "component": "Unspecified", "cxxModuleBmiTarget": {"id": "orchestration_comp::@b474fdd263f216d3bed2", "index": 97}, "destination": "orchestration/bin", "type": "cxxModuleBmi"}, {"backtrace": 3, "component": "Unspecified", "destination": "orchestration/certificate", "paths": ["nodes/orchestration/package/certificate/ngen.body.crt"], "type": "file"}, {"backtrace": 4, "component": "Unspecified", "destination": "orchestration/certificate", "paths": ["nodes/orchestration/package/certificate/public-keys/cloud-ngen.pem"], "type": "file"}, {"backtrace": 5, "component": "Unspecified", "destination": "orchestration/certificate", "paths": ["nodes/orchestration/package/certificate/public-keys/dev-i2.pem"], "type": "file"}, {"backtrace": 6, "component": "Unspecified", "destination": "orchestration/certificate", "paths": ["nodes/orchestration/package/certificate/public-keys/i2.pem"], "type": "file"}, {"backtrace": 7, "component": "Unspecified", "destination": "orchestration/certificate", "paths": ["nodes/orchestration/package/certificate/public-keys/stg-i2.pem"], "type": "file"}, {"backtrace": 8, "component": "Unspecified", "destination": "orchestration/lib/boost", "paths": ["/usr/lib/x86_64-linux-gnu/libboost_regex.so", "/usr/lib/x86_64-linux-gnu/libboost_regex.so.1.83.0"], "type": "file"}, {"backtrace": 9, "component": "Unspecified", "destination": "orchestration/lib/boost", "paths": ["/usr/lib/x86_64-linux-gnu/libboost_atomic.so", "/usr/lib/x86_64-linux-gnu/libboost_atomic.so.1.83.0"], "type": "file"}, {"backtrace": 10, "component": "Unspecified", "destination": "orchestration/lib/boost", "paths": ["/usr/lib/x86_64-linux-gnu/libboost_chrono.so", "/usr/lib/x86_64-linux-gnu/libboost_chrono.so.1.83.0"], "type": "file"}, {"backtrace": 11, "component": "Unspecified", "destination": "orchestration/lib/boost", "paths": ["/usr/lib/x86_64-linux-gnu/libboost_context.so.1.83.0", "/usr/lib/x86_64-linux-gnu/libboost_context.so"], "type": "file"}, {"backtrace": 12, "component": "Unspecified", "destination": "orchestration/lib/boost", "paths": ["/usr/lib/x86_64-linux-gnu/libboost_filesystem.so", "/usr/lib/x86_64-linux-gnu/libboost_filesystem.so.1.83.0"], "type": "file"}, {"backtrace": 13, "component": "Unspecified", "destination": "orchestration/lib/boost", "paths": ["/usr/lib/x86_64-linux-gnu/libboost_iostreams.so.1.83.0", "/usr/lib/x86_64-linux-gnu/libboost_iostreams.so"], "type": "file"}, {"backtrace": 14, "component": "Unspecified", "destination": "orchestration/lib/boost", "paths": ["/usr/lib/x86_64-linux-gnu/libboost_system.so", "/usr/lib/x86_64-linux-gnu/libboost_system.so.1.83.0"], "type": "file"}, {"backtrace": 15, "component": "Unspecified", "destination": "orchestration/lib/boost", "paths": ["/usr/lib/x86_64-linux-gnu/libboost_system.so", "/usr/lib/x86_64-linux-gnu/libboost_system.so.1.83.0"], "type": "file"}, {"backtrace": 16, "component": "Unspecified", "destination": "orchestration/lib/boost", "paths": ["/usr/lib/x86_64-linux-gnu/libboost_thread.so.1.83.0", "/usr/lib/x86_64-linux-gnu/libboost_thread.so"], "type": "file"}, {"backtrace": 17, "component": "Unspecified", "destination": "orchestration/lib", "paths": ["/usr/lib/x86_64-linux-gnu/libcrypto.so.3", "/usr/lib/x86_64-linux-gnu/libcrypto.so"], "type": "file"}, {"backtrace": 18, "component": "Unspecified", "destination": "orchestration/lib", "paths": ["/usr/lib/x86_64-linux-gnu/libssl.so", "/usr/lib/x86_64-linux-gnu/libssl.so.3"], "type": "file"}, {"backtrace": 19, "component": "Unspecified", "destination": "orchestration/lib", "paths": ["/usr/lib/x86_64-linux-gnu/libcurl.so.4.8.0", "/usr/lib/x86_64-linux-gnu/libcurl.so", "/usr/lib/x86_64-linux-gnu/libcurl.so.4"], "type": "file"}, {"backtrace": 20, "component": "Unspecified", "destination": "orchestration/lib", "paths": ["/usr/lib/x86_64-linux-gnu/libcurl.so.4.8.0", "/usr/lib/x86_64-linux-gnu/libcurl.so", "/usr/lib/x86_64-linux-gnu/libcurl.so.4"], "type": "file"}, {"backtrace": 21, "component": "Unspecified", "destination": "orchestration/lib", "paths": ["/usr/lib/x86_64-linux-gnu/libz.so.1.3", "/usr/lib/x86_64-linux-gnu/libz.so", "/usr/lib/x86_64-linux-gnu/libz.so.1"], "type": "file"}, {"backtrace": 22, "component": "Unspecified", "destination": "orchestration", "paths": ["nodes/orchestration/scripts"], "type": "directory"}], "paths": {"build": "nodes/orchestration", "source": "nodes/orchestration"}}