{"backtraceGraph": {"commands": ["install"], "files": ["build_system/docker/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 1, "parent": 0}]}, "installers": [{"backtrace": 1, "component": "Unspecified", "destination": ".", "paths": ["build_system/docker/Dockerfile", "build_system/docker/entry.sh", "build_system/docker/install-cp-agent-intelligence-service.sh", "build_system/docker/install-cp-crowdsec-aux.sh", "build_system/docker/self_managed_openappsec_manifest.json"], "type": "file"}], "paths": {"build": "build_system/docker", "source": "build_system/docker"}}