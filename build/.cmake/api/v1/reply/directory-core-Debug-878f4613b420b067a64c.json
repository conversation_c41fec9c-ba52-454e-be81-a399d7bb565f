{"backtraceGraph": {"commands": ["install"], "files": ["core/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 49, "parent": 0}, {"command": 0, "file": 0, "line": 50, "parent": 0}]}, "installers": [{"backtrace": 1, "component": "Unspecified", "destination": "lib", "paths": ["core/libngen_core.so"], "targetId": "ngen_core::@57760688d1f824db5d9c", "targetIndex": 89, "type": "target"}, {"backtrace": 1, "component": "Unspecified", "cxxModuleBmiTarget": {"id": "ngen_core::@57760688d1f824db5d9c", "index": 89}, "destination": "lib", "type": "cxxModuleBmi"}, {"backtrace": 2, "component": "Unspecified", "destination": "orchestration/lib", "paths": ["core/libngen_core.so"], "targetId": "ngen_core::@57760688d1f824db5d9c", "targetIndex": 89, "type": "target"}, {"backtrace": 2, "component": "Unspecified", "cxxModuleBmiTarget": {"id": "ngen_core::@57760688d1f824db5d9c", "index": 89}, "destination": "orchestration/lib", "type": "cxxModuleBmi"}], "paths": {"build": "core", "source": "core"}}