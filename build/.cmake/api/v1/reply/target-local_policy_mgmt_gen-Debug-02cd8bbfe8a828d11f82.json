{"archive": {}, "artifacts": [{"path": "components/security_apps/local_policy_mgmt_gen/liblocal_policy_mgmt_gen.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "include_directories"], "files": ["components/security_apps/local_policy_mgmt_gen/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 2, "parent": 0}, {"file": 1}, {"command": 1, "file": 1, "line": 21, "parent": 2}, {"command": 1, "file": 1, "line": 22, "parent": 2}, {"command": 1, "file": 1, "line": 26, "parent": 2}, {"command": 1, "file": 1, "line": 27, "parent": 2}, {"command": 1, "file": 1, "line": 28, "parent": 2}, {"command": 1, "file": 1, "line": 29, "parent": 2}, {"command": 1, "file": 1, "line": 30, "parent": 2}, {"command": 1, "file": 1, "line": 31, "parent": 2}, {"command": 1, "file": 1, "line": 32, "parent": 2}, {"command": 1, "file": 1, "line": 33, "parent": 2}, {"command": 1, "file": 1, "line": 34, "parent": 2}, {"command": 1, "file": 1, "line": 35, "parent": 2}, {"command": 1, "file": 1, "line": 36, "parent": 2}, {"command": 1, "file": 1, "line": 37, "parent": 2}, {"command": 1, "file": 0, "line": 1, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -O2 -fPIC -Wall -Wno-terminate -g"}], "includes": [{"backtrace": 3, "path": "/usr/include/libxml2"}, {"backtrace": 4, "path": "/usr/src/googletest/googlemock/include"}, {"backtrace": 5, "path": "/home/<USER>/Code/openappsec/external"}, {"backtrace": 6, "path": "/home/<USER>/Code/openappsec/external/yajl/yajl-2.1.1/include"}, {"backtrace": 7, "path": "/home/<USER>/Code/openappsec/external/C-Mock/include/cmock"}, {"backtrace": 8, "path": "/home/<USER>/Code/openappsec/external/picojson"}, {"backtrace": 9, "path": "/home/<USER>/Code/openappsec/core/include/general"}, {"backtrace": 10, "path": "/home/<USER>/Code/openappsec/core/include/internal"}, {"backtrace": 11, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/interfaces"}, {"backtrace": 12, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/resources"}, {"backtrace": 13, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/utilities"}, {"backtrace": 14, "path": "/home/<USER>/Code/openappsec/core/include/attachments"}, {"backtrace": 15, "path": "/home/<USER>/Code/openappsec/events/include"}, {"backtrace": 16, "path": "/home/<USER>/Code/openappsec/components/include"}, {"backtrace": 17, "path": "/home/<USER>/Code/openappsec/components/security_apps/local_policy_mgmt_gen/include"}], "language": "CXX", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22]}], "id": "local_policy_mgmt_gen::@0a40031ee6fe94db94bb", "name": "local_policy_mgmt_gen", "nameOnDisk": "liblocal_policy_mgmt_gen.a", "paths": {"build": "components/security_apps/local_policy_mgmt_gen", "source": "components/security_apps/local_policy_mgmt_gen"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/local_policy_mgmt_gen/appsec_practice_section.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/local_policy_mgmt_gen/exceptions_section.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/local_policy_mgmt_gen/ingress_data.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/local_policy_mgmt_gen/rules_config_section.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/local_policy_mgmt_gen/settings_section.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/local_policy_mgmt_gen/snort_section.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/local_policy_mgmt_gen/triggers_section.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/local_policy_mgmt_gen/trusted_sources_section.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/local_policy_mgmt_gen/policy_maker_utils.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/local_policy_mgmt_gen/k8s_policy_utils.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/local_policy_mgmt_gen/local_policy_mgmt_gen.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/local_policy_mgmt_gen/new_appsec_policy_crd_parser.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/local_policy_mgmt_gen/new_appsec_linux_policy.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/local_policy_mgmt_gen/new_auto_upgrade.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/local_policy_mgmt_gen/new_custom_response.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/local_policy_mgmt_gen/new_trusted_sources.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/local_policy_mgmt_gen/new_log_trigger.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/local_policy_mgmt_gen/new_practice.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/local_policy_mgmt_gen/new_exceptions.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/local_policy_mgmt_gen/access_control_practice.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/local_policy_mgmt_gen/configmaps.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/local_policy_mgmt_gen/reverse_proxy_section.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/local_policy_mgmt_gen/policy_activation_data.cc", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}