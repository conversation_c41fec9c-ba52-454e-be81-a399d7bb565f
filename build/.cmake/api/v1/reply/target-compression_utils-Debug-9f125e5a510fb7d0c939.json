{"artifacts": [{"path": "core/compression/libcompression_utils.so"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "install", "link_directories", "add_definitions", "include_directories"], "files": ["core/compression/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 4, "parent": 0}, {"command": 1, "file": 0, "line": 9, "parent": 0}, {"command": 1, "file": 0, "line": 10, "parent": 0}, {"file": 1}, {"command": 2, "file": 1, "line": 20, "parent": 4}, {"command": 3, "file": 0, "line": 2, "parent": 0}, {"command": 4, "file": 1, "line": 21, "parent": 4}, {"command": 4, "file": 1, "line": 22, "parent": 4}, {"command": 4, "file": 1, "line": 26, "parent": 4}, {"command": 4, "file": 1, "line": 27, "parent": 4}, {"command": 4, "file": 1, "line": 28, "parent": 4}, {"command": 4, "file": 1, "line": 29, "parent": 4}, {"command": 4, "file": 1, "line": 30, "parent": 4}, {"command": 4, "file": 1, "line": 31, "parent": 4}, {"command": 4, "file": 1, "line": 32, "parent": 4}, {"command": 4, "file": 1, "line": 33, "parent": 4}, {"command": 4, "file": 1, "line": 34, "parent": 4}, {"command": 4, "file": 1, "line": 35, "parent": 4}, {"command": 4, "file": 1, "line": 36, "parent": 4}, {"command": 4, "file": 1, "line": 37, "parent": 4}, {"command": 4, "file": 0, "line": 1, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -O2 -fPIC -Wall -Wno-terminate -g -fPIC"}], "defines": [{"backtrace": 6, "define": "ZLIB_CONST"}, {"define": "compression_utils_EXPORTS"}], "includes": [{"backtrace": 7, "path": "/usr/include/libxml2"}, {"backtrace": 8, "path": "/usr/src/googletest/googlemock/include"}, {"backtrace": 9, "path": "/home/<USER>/Code/openappsec/external"}, {"backtrace": 10, "path": "/home/<USER>/Code/openappsec/external/yajl/yajl-2.1.1/include"}, {"backtrace": 11, "path": "/home/<USER>/Code/openappsec/external/C-Mock/include/cmock"}, {"backtrace": 12, "path": "/home/<USER>/Code/openappsec/external/picojson"}, {"backtrace": 13, "path": "/home/<USER>/Code/openappsec/core/include/general"}, {"backtrace": 14, "path": "/home/<USER>/Code/openappsec/core/include/internal"}, {"backtrace": 15, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/interfaces"}, {"backtrace": 16, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/resources"}, {"backtrace": 17, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/utilities"}, {"backtrace": 18, "path": "/home/<USER>/Code/openappsec/core/include/attachments"}, {"backtrace": 19, "path": "/home/<USER>/Code/openappsec/events/include"}, {"backtrace": 20, "path": "/home/<USER>/Code/openappsec/components/include"}, {"backtrace": 21, "path": "/include"}], "language": "CXX", "sourceIndexes": [0]}], "id": "compression_utils::@3dcc85d11fba6ff84d38", "install": {"destinations": [{"backtrace": 2, "path": "lib"}, {"backtrace": 2, "path": "lib"}, {"backtrace": 3, "path": "http_transaction_handler_service/lib"}, {"backtrace": 3, "path": "http_transaction_handler_service/lib"}], "prefix": {"path": "/usr/local"}}, "link": {"commandFragments": [{"fragment": "", "role": "flags"}, {"backtrace": 5, "fragment": "-L/usr/lib/x86_64-linux-gnu/libz.so", "role": "libraryPath"}], "language": "CXX"}, "name": "compression_utils", "nameOnDisk": "libcompression_utils.so", "paths": {"build": "core/compression", "source": "core/compression"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "core/compression/compression_utils.cc", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}