{"backtraceGraph": {"commands": ["install"], "files": ["nodes/http_transaction_handler/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 49, "parent": 0}, {"command": 0, "file": 0, "line": 50, "parent": 0}, {"command": 0, "file": 0, "line": 56, "parent": 0}, {"command": 0, "file": 0, "line": 62, "parent": 0}, {"command": 0, "file": 0, "line": 68, "parent": 0}, {"command": 0, "file": 0, "line": 74, "parent": 0}, {"command": 0, "file": 0, "line": 80, "parent": 0}]}, "installers": [{"backtrace": 1, "component": "Unspecified", "destination": "bin", "paths": ["nodes/http_transaction_handler/cp-nano-http-transaction-handler"], "targetId": "cp-nano-http-transaction-handler::@6a2d719aa39f4aee4b1d", "targetIndex": 20, "type": "target"}, {"backtrace": 1, "component": "Unspecified", "cxxModuleBmiTarget": {"id": "cp-nano-http-transaction-handler::@6a2d719aa39f4aee4b1d", "index": 20}, "destination": "bin", "type": "cxxModuleBmi"}, {"backtrace": 2, "component": "Unspecified", "destination": "http_transaction_handler_service/bin", "paths": ["nodes/http_transaction_handler/cp-nano-http-transaction-handler"], "targetId": "cp-nano-http-transaction-handler::@6a2d719aa39f4aee4b1d", "targetIndex": 20, "type": "target"}, {"backtrace": 2, "component": "Unspecified", "cxxModuleBmiTarget": {"id": "cp-nano-http-transaction-handler::@6a2d719aa39f4aee4b1d", "index": 20}, "destination": "http_transaction_handler_service/bin", "type": "cxxModuleBmi"}, {"backtrace": 3, "component": "Unspecified", "destination": "http_transaction_handler_service/lib", "paths": ["/usr/lib/x86_64-linux-gnu/libpcre2-8.so.0.11.2", "/usr/lib/x86_64-linux-gnu/libpcre2-8.so", "/usr/lib/x86_64-linux-gnu/libpcre2-8.so.0"], "type": "file"}, {"backtrace": 4, "component": "Unspecified", "destination": "http_transaction_handler_service/lib", "paths": ["/usr/lib/x86_64-linux-gnu/libpcre2-posix.so", "/usr/lib/x86_64-linux-gnu/libpcre2-posix.so.3.0.4", "/usr/lib/x86_64-linux-gnu/libpcre2-posix.so.3"], "type": "file"}, {"backtrace": 5, "component": "Unspecified", "destination": "http_transaction_handler_service/lib", "paths": ["/usr/lib/x86_64-linux-gnu/libhiredis.so.1.1.0", "/usr/lib/x86_64-linux-gnu/libhiredis.so", "/usr/lib/x86_64-linux-gnu/libhiredis.so.1"], "type": "file"}, {"backtrace": 6, "component": "Unspecified", "destination": "http_transaction_handler_service/lib", "paths": ["/usr/lib/x86_64-linux-gnu/libxml2.so.2", "/usr/lib/x86_64-linux-gnu/libxml2.so.2.9.14", "/usr/lib/x86_64-linux-gnu/libxml2.so"], "type": "file"}, {"backtrace": 7, "component": "Unspecified", "destination": "http_transaction_handler_service/lib", "paths": ["/usr/lib/x86_64-linux-gnu/libmaxminddb.so", "/usr/lib/x86_64-linux-gnu/libmaxminddb.so.0", "/usr/lib/x86_64-linux-gnu/libmaxminddb.so.0.0.7"], "type": "file"}], "paths": {"build": "nodes/http_transaction_handler", "source": "nodes/http_transaction_handler"}}