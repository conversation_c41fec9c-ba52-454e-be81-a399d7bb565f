{"artifacts": [{"path": "core/shm_pkt_queue/libshm_pkt_queue.so"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "install", "link_directories", "target_link_libraries", "include_directories"], "files": ["core/shm_pkt_queue/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 7, "parent": 0}, {"command": 1, "file": 0, "line": 11, "parent": 0}, {"file": 1}, {"command": 2, "file": 1, "line": 20, "parent": 3}, {"command": 2, "file": 0, "line": 6, "parent": 0}, {"command": 3, "file": 0, "line": 9, "parent": 0}, {"command": 4, "file": 1, "line": 21, "parent": 3}, {"command": 4, "file": 1, "line": 22, "parent": 3}, {"command": 4, "file": 1, "line": 26, "parent": 3}, {"command": 4, "file": 1, "line": 27, "parent": 3}, {"command": 4, "file": 1, "line": 28, "parent": 3}, {"command": 4, "file": 1, "line": 29, "parent": 3}, {"command": 4, "file": 1, "line": 30, "parent": 3}, {"command": 4, "file": 1, "line": 31, "parent": 3}, {"command": 4, "file": 1, "line": 32, "parent": 3}, {"command": 4, "file": 1, "line": 33, "parent": 3}, {"command": 4, "file": 1, "line": 34, "parent": 3}, {"command": 4, "file": 1, "line": 35, "parent": 3}, {"command": 4, "file": 1, "line": 36, "parent": 3}, {"command": 4, "file": 1, "line": 37, "parent": 3}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -O2 -fPIC -Wall -Wno-terminate -g -fPIC   -Wno-strict-aliasing -Wno-class-memaccess -Wno-maybe-uninitialized"}], "defines": [{"define": "shm_pkt_queue_EXPORTS"}], "includes": [{"backtrace": 7, "path": "/usr/include/libxml2"}, {"backtrace": 8, "path": "/usr/src/googletest/googlemock/include"}, {"backtrace": 9, "path": "/home/<USER>/Code/openappsec/external"}, {"backtrace": 10, "path": "/home/<USER>/Code/openappsec/external/yajl/yajl-2.1.1/include"}, {"backtrace": 11, "path": "/home/<USER>/Code/openappsec/external/C-Mock/include/cmock"}, {"backtrace": 12, "path": "/home/<USER>/Code/openappsec/external/picojson"}, {"backtrace": 13, "path": "/home/<USER>/Code/openappsec/core/include/general"}, {"backtrace": 14, "path": "/home/<USER>/Code/openappsec/core/include/internal"}, {"backtrace": 15, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/interfaces"}, {"backtrace": 16, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/resources"}, {"backtrace": 17, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/utilities"}, {"backtrace": 18, "path": "/home/<USER>/Code/openappsec/core/include/attachments"}, {"backtrace": 19, "path": "/home/<USER>/Code/openappsec/events/include"}, {"backtrace": 20, "path": "/home/<USER>/Code/openappsec/components/include"}], "language": "CXX", "sourceIndexes": [0]}], "id": "shm_pkt_queue::@a805dd128f5d38de9bfb", "install": {"destinations": [{"backtrace": 2, "path": "lib"}, {"backtrace": 2, "path": "lib"}], "prefix": {"path": "/usr/local"}}, "link": {"commandFragments": [{"fragment": "", "role": "flags"}, {"backtrace": 4, "fragment": "-L/usr/lib/x86_64-linux-gnu/libz.so", "role": "libraryPath"}, {"backtrace": 5, "fragment": "-L/nginx_cache_shmem", "role": "libraryPath"}, {"fragment": "-Wl,-r<PERSON>,/usr/lib/x86_64-linux-gnu/libz.so:/nginx_cache_shmem:", "role": "libraries"}, {"backtrace": 6, "fragment": "-lrt", "role": "libraries"}], "language": "CXX"}, "name": "shm_pkt_queue", "nameOnDisk": "libshm_pkt_queue.so", "paths": {"build": "core/shm_pkt_queue", "source": "core/shm_pkt_queue"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "core/shm_pkt_queue/shm_pkt_queue.cc", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}