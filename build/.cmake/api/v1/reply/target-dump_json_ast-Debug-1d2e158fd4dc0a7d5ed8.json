{"artifacts": [{"path": "external/graphqlparser/dump_json_ast"}], "backtrace": 1, "backtraceGraph": {"commands": ["ADD_EXECUTABLE", "link_directories", "TARGET_LINK_LIBRARIES", "include_directories", "INCLUDE_DIRECTORIES"], "files": ["external/graphqlparser/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 82, "parent": 0}, {"file": 1}, {"command": 1, "file": 1, "line": 20, "parent": 2}, {"command": 2, "file": 0, "line": 83, "parent": 0}, {"command": 3, "file": 1, "line": 21, "parent": 2}, {"command": 3, "file": 1, "line": 22, "parent": 2}, {"command": 3, "file": 1, "line": 26, "parent": 2}, {"command": 3, "file": 1, "line": 27, "parent": 2}, {"command": 3, "file": 1, "line": 28, "parent": 2}, {"command": 3, "file": 1, "line": 29, "parent": 2}, {"command": 3, "file": 1, "line": 30, "parent": 2}, {"command": 3, "file": 1, "line": 31, "parent": 2}, {"command": 3, "file": 1, "line": 32, "parent": 2}, {"command": 3, "file": 1, "line": 33, "parent": 2}, {"command": 3, "file": 1, "line": 34, "parent": 2}, {"command": 3, "file": 1, "line": 35, "parent": 2}, {"command": 3, "file": 1, "line": 36, "parent": 2}, {"command": 3, "file": 1, "line": 37, "parent": 2}, {"command": 4, "file": 0, "line": 55, "parent": 0}, {"command": 4, "file": 0, "line": 56, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -O2 -fPIC -Wall -Wno-terminate -std=gnu++11 -g"}], "includes": [{"backtrace": 5, "path": "/usr/include/libxml2"}, {"backtrace": 6, "path": "/usr/src/googletest/googlemock/include"}, {"backtrace": 7, "path": "/home/<USER>/Code/openappsec/external"}, {"backtrace": 8, "path": "/home/<USER>/Code/openappsec/external/yajl/yajl-2.1.1/include"}, {"backtrace": 9, "path": "/home/<USER>/Code/openappsec/external/C-Mock/include/cmock"}, {"backtrace": 10, "path": "/home/<USER>/Code/openappsec/external/picojson"}, {"backtrace": 11, "path": "/home/<USER>/Code/openappsec/core/include/general"}, {"backtrace": 12, "path": "/home/<USER>/Code/openappsec/core/include/internal"}, {"backtrace": 13, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/interfaces"}, {"backtrace": 14, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/resources"}, {"backtrace": 15, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/utilities"}, {"backtrace": 16, "path": "/home/<USER>/Code/openappsec/core/include/attachments"}, {"backtrace": 17, "path": "/home/<USER>/Code/openappsec/events/include"}, {"backtrace": 18, "path": "/home/<USER>/Code/openappsec/components/include"}, {"backtrace": 19, "path": "/home/<USER>/Code/openappsec/external/graphqlparser"}, {"backtrace": 20, "path": "/home/<USER>/Code/openappsec/build/external/graphqlparser"}], "language": "CXX", "sourceIndexes": [0]}], "dependencies": [{"backtrace": 4, "id": "graphqlparser::@b5ec2cb062faa4171e82"}], "id": "dump_json_ast::@b5ec2cb062faa4171e82", "link": {"commandFragments": [{"fragment": "-O2 -fPIC -Wall -Wno-terminate -std=gnu++11 -g", "role": "flags"}, {"fragment": "-rdynamic", "role": "flags"}, {"backtrace": 3, "fragment": "-L/usr/lib/x86_64-linux-gnu/libz.so", "role": "libraryPath"}, {"fragment": "-Wl,-r<PERSON>,/usr/lib/x86_64-linux-gnu/libz.so", "role": "libraries"}, {"backtrace": 4, "fragment": "libgraphqlparser.a", "role": "libraries"}], "language": "CXX"}, "name": "dump_json_ast", "nameOnDisk": "dump_json_ast", "paths": {"build": "external/graphqlparser", "source": "external/graphqlparser"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "external/graphqlparser/dump_json_ast.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}