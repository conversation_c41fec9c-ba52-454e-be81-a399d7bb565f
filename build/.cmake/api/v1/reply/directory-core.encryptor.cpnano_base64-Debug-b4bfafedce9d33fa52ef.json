{"backtraceGraph": {"commands": ["install"], "files": ["core/encryptor/cpnano_base64/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 5, "parent": 0}, {"command": 0, "file": 0, "line": 6, "parent": 0}]}, "installers": [{"backtrace": 1, "component": "Unspecified", "destination": "bin", "paths": ["core/encryptor/cpnano_base64/cpnano_base64"], "targetId": "cpnano_base64::@54c975be1dba3c8d2a85", "targetIndex": 21, "type": "target"}, {"backtrace": 1, "component": "Unspecified", "cxxModuleBmiTarget": {"id": "cpnano_base64::@54c975be1dba3c8d2a85", "index": 21}, "destination": "bin", "type": "cxxModuleBmi"}, {"backtrace": 2, "component": "Unspecified", "destination": "orchestration", "paths": ["core/encryptor/cpnano_base64/cpnano_base64"], "targetId": "cpnano_base64::@54c975be1dba3c8d2a85", "targetIndex": 21, "type": "target"}, {"backtrace": 2, "component": "Unspecified", "cxxModuleBmiTarget": {"id": "cpnano_base64::@54c975be1dba3c8d2a85", "index": 21}, "destination": "orchestration", "type": "cxxModuleBmi"}], "paths": {"build": "core/encryptor/cpnano_base64", "source": "core/encryptor/cpnano_base64"}}