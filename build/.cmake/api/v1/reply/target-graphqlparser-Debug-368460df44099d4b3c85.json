{"archive": {}, "artifacts": [{"path": "external/graphqlparser/libgraphqlparser.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["ADD_LIBRARY", "include_directories", "INCLUDE_DIRECTORIES"], "files": ["external/graphqlparser/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 58, "parent": 0}, {"file": 1}, {"command": 1, "file": 1, "line": 21, "parent": 2}, {"command": 1, "file": 1, "line": 22, "parent": 2}, {"command": 1, "file": 1, "line": 26, "parent": 2}, {"command": 1, "file": 1, "line": 27, "parent": 2}, {"command": 1, "file": 1, "line": 28, "parent": 2}, {"command": 1, "file": 1, "line": 29, "parent": 2}, {"command": 1, "file": 1, "line": 30, "parent": 2}, {"command": 1, "file": 1, "line": 31, "parent": 2}, {"command": 1, "file": 1, "line": 32, "parent": 2}, {"command": 1, "file": 1, "line": 33, "parent": 2}, {"command": 1, "file": 1, "line": 34, "parent": 2}, {"command": 1, "file": 1, "line": 35, "parent": 2}, {"command": 1, "file": 1, "line": 36, "parent": 2}, {"command": 1, "file": 1, "line": 37, "parent": 2}, {"command": 2, "file": 0, "line": 55, "parent": 0}, {"command": 2, "file": 0, "line": 56, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -O2 -fPIC -Wall -Wno-terminate -std=gnu++11 -g"}], "includes": [{"backtrace": 3, "path": "/usr/include/libxml2"}, {"backtrace": 4, "path": "/usr/src/googletest/googlemock/include"}, {"backtrace": 5, "path": "/home/<USER>/Code/openappsec/external"}, {"backtrace": 6, "path": "/home/<USER>/Code/openappsec/external/yajl/yajl-2.1.1/include"}, {"backtrace": 7, "path": "/home/<USER>/Code/openappsec/external/C-Mock/include/cmock"}, {"backtrace": 8, "path": "/home/<USER>/Code/openappsec/external/picojson"}, {"backtrace": 9, "path": "/home/<USER>/Code/openappsec/core/include/general"}, {"backtrace": 10, "path": "/home/<USER>/Code/openappsec/core/include/internal"}, {"backtrace": 11, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/interfaces"}, {"backtrace": 12, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/resources"}, {"backtrace": 13, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/utilities"}, {"backtrace": 14, "path": "/home/<USER>/Code/openappsec/core/include/attachments"}, {"backtrace": 15, "path": "/home/<USER>/Code/openappsec/events/include"}, {"backtrace": 16, "path": "/home/<USER>/Code/openappsec/components/include"}, {"backtrace": 17, "path": "/home/<USER>/Code/openappsec/external/graphqlparser"}, {"backtrace": 18, "path": "/home/<USER>/Code/openappsec/build/external/graphqlparser"}], "language": "CXX", "sourceIndexes": [0, 2, 5, 9, 11, 12, 13, 15, 16, 17]}], "id": "graphqlparser::@b5ec2cb062faa4171e82", "name": "graphqlparser", "nameOnDisk": "libgraphqlparser.a", "paths": {"build": "external/graphqlparser", "source": "external/graphqlparser"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 2, 5, 9, 11, 12, 13, 15, 16, 17]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1, 3, 4, 6, 10, 14]}, {"name": "", "sourceIndexes": [7, 8]}, {"name": "CMake Rules", "sourceIndexes": [18, 19, 20, 21, 22, 23, 24, 25, 26, 27]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "external/graphqlparser/JsonVisitor.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "isGenerated": true, "path": "build/external/graphqlparser/Ast.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "isGenerated": true, "path": "build/external/graphqlparser/Ast.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "isGenerated": true, "path": "build/external/graphqlparser/AstVisitor.h", "sourceGroupIndex": 1}, {"backtrace": 1, "isGenerated": true, "path": "build/external/graphqlparser/c/GraphQLAst.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "isGenerated": true, "path": "build/external/graphqlparser/c/GraphQLAst.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "isGenerated": true, "path": "build/external/graphqlparser/c/GraphQLAstForEachConcreteType.h", "sourceGroupIndex": 1}, {"backtrace": 1, "isGenerated": true, "path": "build/external/graphqlparser/JsonVisitor.h.inc", "sourceGroupIndex": 2}, {"backtrace": 1, "isGenerated": true, "path": "build/external/graphqlparser/JsonVisitor.cpp.inc", "sourceGroupIndex": 2}, {"backtrace": 1, "compileGroupIndex": 0, "isGenerated": true, "path": "build/external/graphqlparser/parser.tab.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "isGenerated": true, "path": "build/external/graphqlparser/parser.tab.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "isGenerated": true, "path": "build/external/graphqlparser/lexer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "external/graphqlparser/c/GraphQLAstNode.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "external/graphqlparser/c/GraphQLAstToJSON.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "external/graphqlparser/c/GraphQLAstVisitor.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "external/graphqlparser/c/GraphQLAstVisitor.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "external/graphqlparser/c/GraphQLParser.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "external/graphqlparser/GraphQLParser.cpp", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/external/graphqlparser/Ast.h.rule", "sourceGroupIndex": 3}, {"backtrace": 0, "isGenerated": true, "path": "build/external/graphqlparser/Ast.cpp.rule", "sourceGroupIndex": 3}, {"backtrace": 0, "isGenerated": true, "path": "build/external/graphqlparser/AstVisitor.h.rule", "sourceGroupIndex": 3}, {"backtrace": 0, "isGenerated": true, "path": "build/external/graphqlparser/c/GraphQLAst.h.rule", "sourceGroupIndex": 3}, {"backtrace": 0, "isGenerated": true, "path": "build/external/graphqlparser/c/GraphQLAst.cpp.rule", "sourceGroupIndex": 3}, {"backtrace": 0, "isGenerated": true, "path": "build/external/graphqlparser/c/GraphQLAstForEachConcreteType.h.rule", "sourceGroupIndex": 3}, {"backtrace": 0, "isGenerated": true, "path": "build/external/graphqlparser/JsonVisitor.h.inc.rule", "sourceGroupIndex": 3}, {"backtrace": 0, "isGenerated": true, "path": "build/external/graphqlparser/JsonVisitor.cpp.inc.rule", "sourceGroupIndex": 3}, {"backtrace": 0, "isGenerated": true, "path": "build/external/graphqlparser/parser.tab.cpp.rule", "sourceGroupIndex": 3}, {"backtrace": 0, "isGenerated": true, "path": "build/external/graphqlparser/lexer.cpp.rule", "sourceGroupIndex": 3}], "type": "STATIC_LIBRARY"}