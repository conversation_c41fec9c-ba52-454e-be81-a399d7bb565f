{"archive": {}, "artifacts": [{"path": "external/yajl/yajl-2.1.1/lib/libyajl_s.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["ADD_LIBRARY", "ADD_DEFINITIONS", "include_directories", "INCLUDE_DIRECTORIES"], "files": ["external/yajl/src/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 39, "parent": 0}, {"command": 1, "file": 0, "line": 28, "parent": 0}, {"file": 1}, {"command": 2, "file": 1, "line": 21, "parent": 3}, {"command": 2, "file": 1, "line": 22, "parent": 3}, {"command": 2, "file": 1, "line": 26, "parent": 3}, {"command": 2, "file": 1, "line": 27, "parent": 3}, {"command": 2, "file": 1, "line": 28, "parent": 3}, {"command": 2, "file": 1, "line": 29, "parent": 3}, {"command": 2, "file": 1, "line": 30, "parent": 3}, {"command": 2, "file": 1, "line": 31, "parent": 3}, {"command": 2, "file": 1, "line": 32, "parent": 3}, {"command": 2, "file": 1, "line": 33, "parent": 3}, {"command": 2, "file": 1, "line": 34, "parent": 3}, {"command": 2, "file": 1, "line": 35, "parent": 3}, {"command": 2, "file": 1, "line": 36, "parent": 3}, {"command": 2, "file": 1, "line": 37, "parent": 3}, {"command": 3, "file": 0, "line": 77, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -Wall -std=c99 -pedantic -Wpointer-arith -Wno-format-y2k -Wstrict-prototypes -Wmissing-declarations -Wnested-externs -Wextra\t-Wundef -Wwrite-strings -Wold-style-definition -Wredundant-decls -Wno-unused-parameter -Wno-sign-compare -Wmissing-prototypes -DDEBUG -g   -Wno-implicit-fallthrough"}], "defines": [{"backtrace": 2, "define": "YAJL_BUILD"}], "includes": [{"backtrace": 4, "path": "/usr/include/libxml2"}, {"backtrace": 5, "path": "/usr/src/googletest/googlemock/include"}, {"backtrace": 6, "path": "/home/<USER>/Code/openappsec/external"}, {"backtrace": 7, "path": "/home/<USER>/Code/openappsec/external/yajl/yajl-2.1.1/include"}, {"backtrace": 8, "path": "/home/<USER>/Code/openappsec/external/C-Mock/include/cmock"}, {"backtrace": 9, "path": "/home/<USER>/Code/openappsec/external/picojson"}, {"backtrace": 10, "path": "/home/<USER>/Code/openappsec/core/include/general"}, {"backtrace": 11, "path": "/home/<USER>/Code/openappsec/core/include/internal"}, {"backtrace": 12, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/interfaces"}, {"backtrace": 13, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/resources"}, {"backtrace": 14, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/utilities"}, {"backtrace": 15, "path": "/home/<USER>/Code/openappsec/core/include/attachments"}, {"backtrace": 16, "path": "/home/<USER>/Code/openappsec/events/include"}, {"backtrace": 17, "path": "/home/<USER>/Code/openappsec/components/include"}, {"backtrace": 18, "path": "/home/<USER>/Code/openappsec/build/external/yajl/src/../yajl-2.1.1/include/yajl/.."}], "language": "C", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8]}], "id": "yajl_s::@971c39514bf700832d90", "name": "yajl_s", "nameOnDisk": "libyajl_s.a", "paths": {"build": "external/yajl/src", "source": "external/yajl/src"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [9, 10, 11, 12, 13, 14, 15, 16, 17]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "external/yajl/src/yajl.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "external/yajl/src/yajl_lex.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "external/yajl/src/yajl_parser.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "external/yajl/src/yajl_buf.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "external/yajl/src/yajl_encode.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "external/yajl/src/yajl_gen.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "external/yajl/src/yajl_alloc.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "external/yajl/src/yajl_tree.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "external/yajl/src/yajl_version.c", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "external/yajl/src/yajl_parser.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "external/yajl/src/yajl_lex.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "external/yajl/src/yajl_buf.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "external/yajl/src/yajl_encode.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "external/yajl/src/yajl_alloc.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "external/yajl/src/api/yajl_parse.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "external/yajl/src/api/yajl_gen.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "external/yajl/src/api/yajl_common.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "external/yajl/src/api/yajl_tree.h", "sourceGroupIndex": 1}], "type": "STATIC_LIBRARY"}