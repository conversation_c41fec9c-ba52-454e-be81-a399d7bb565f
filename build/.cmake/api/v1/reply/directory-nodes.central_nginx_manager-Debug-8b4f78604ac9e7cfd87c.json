{"backtraceGraph": {"commands": ["install"], "files": ["nodes/central_nginx_manager/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 26, "parent": 0}, {"command": 0, "file": 0, "line": 27, "parent": 0}]}, "installers": [{"backtrace": 1, "component": "Unspecified", "destination": "bin", "paths": ["nodes/central_nginx_manager/cp-nano-central-nginx-manager"], "targetId": "cp-nano-central-nginx-manager::@2138b9ab414ffd11efe3", "targetIndex": 19, "type": "target"}, {"backtrace": 1, "component": "Unspecified", "cxxModuleBmiTarget": {"id": "cp-nano-central-nginx-manager::@2138b9ab414ffd11efe3", "index": 19}, "destination": "bin", "type": "cxxModuleBmi"}, {"backtrace": 2, "component": "Unspecified", "destination": "central_nginx_manager/bin", "paths": ["nodes/central_nginx_manager/cp-nano-central-nginx-manager"], "targetId": "cp-nano-central-nginx-manager::@2138b9ab414ffd11efe3", "targetIndex": 19, "type": "target"}, {"backtrace": 2, "component": "Unspecified", "cxxModuleBmiTarget": {"id": "cp-nano-central-nginx-manager::@2138b9ab414ffd11efe3", "index": 19}, "destination": "central_nginx_manager/bin", "type": "cxxModuleBmi"}], "paths": {"build": "nodes/central_nginx_manager", "source": "nodes/central_nginx_manager"}}