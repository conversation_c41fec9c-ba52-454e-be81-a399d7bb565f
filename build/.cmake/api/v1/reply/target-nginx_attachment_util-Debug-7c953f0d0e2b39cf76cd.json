{"artifacts": [{"path": "attachments/nginx/nginx_attachment_util/libnginx_attachment_util.so"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "install", "link_directories", "add_definitions", "include_directories"], "files": ["attachments/nginx/nginx_attachment_util/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 3, "parent": 0}, {"command": 1, "file": 0, "line": 7, "parent": 0}, {"command": 1, "file": 0, "line": 8, "parent": 0}, {"file": 1}, {"command": 2, "file": 1, "line": 20, "parent": 4}, {"command": 3, "file": 0, "line": 1, "parent": 0}, {"command": 4, "file": 1, "line": 21, "parent": 4}, {"command": 4, "file": 1, "line": 22, "parent": 4}, {"command": 4, "file": 1, "line": 26, "parent": 4}, {"command": 4, "file": 1, "line": 27, "parent": 4}, {"command": 4, "file": 1, "line": 28, "parent": 4}, {"command": 4, "file": 1, "line": 29, "parent": 4}, {"command": 4, "file": 1, "line": 30, "parent": 4}, {"command": 4, "file": 1, "line": 31, "parent": 4}, {"command": 4, "file": 1, "line": 32, "parent": 4}, {"command": 4, "file": 1, "line": 33, "parent": 4}, {"command": 4, "file": 1, "line": 34, "parent": 4}, {"command": 4, "file": 1, "line": 35, "parent": 4}, {"command": 4, "file": 1, "line": 36, "parent": 4}, {"command": 4, "file": 1, "line": 37, "parent": 4}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -O2 -fPIC -Wall -Wno-terminate -g -fPIC"}], "defines": [{"backtrace": 6, "define": "USERSPACE"}, {"define": "nginx_attachment_util_EXPORTS"}], "includes": [{"backtrace": 7, "path": "/usr/include/libxml2"}, {"backtrace": 8, "path": "/usr/src/googletest/googlemock/include"}, {"backtrace": 9, "path": "/home/<USER>/Code/openappsec/external"}, {"backtrace": 10, "path": "/home/<USER>/Code/openappsec/external/yajl/yajl-2.1.1/include"}, {"backtrace": 11, "path": "/home/<USER>/Code/openappsec/external/C-Mock/include/cmock"}, {"backtrace": 12, "path": "/home/<USER>/Code/openappsec/external/picojson"}, {"backtrace": 13, "path": "/home/<USER>/Code/openappsec/core/include/general"}, {"backtrace": 14, "path": "/home/<USER>/Code/openappsec/core/include/internal"}, {"backtrace": 15, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/interfaces"}, {"backtrace": 16, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/resources"}, {"backtrace": 17, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/utilities"}, {"backtrace": 18, "path": "/home/<USER>/Code/openappsec/core/include/attachments"}, {"backtrace": 19, "path": "/home/<USER>/Code/openappsec/events/include"}, {"backtrace": 20, "path": "/home/<USER>/Code/openappsec/components/include"}], "language": "CXX", "sourceIndexes": [0]}], "id": "nginx_attachment_util::@b089a24a02c75410debb", "install": {"destinations": [{"backtrace": 2, "path": "lib"}, {"backtrace": 2, "path": "lib"}, {"backtrace": 3, "path": "http_transaction_handler_service/lib"}, {"backtrace": 3, "path": "http_transaction_handler_service/lib"}], "prefix": {"path": "/usr/local"}}, "link": {"commandFragments": [{"fragment": "", "role": "flags"}, {"backtrace": 5, "fragment": "-L/usr/lib/x86_64-linux-gnu/libz.so", "role": "libraryPath"}], "language": "CXX"}, "name": "nginx_attachment_util", "nameOnDisk": "libnginx_attachment_util.so", "paths": {"build": "attachments/nginx/nginx_attachment_util", "source": "attachments/nginx/nginx_attachment_util"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "attachments/nginx/nginx_attachment_util/nginx_attachment_util.cc", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}