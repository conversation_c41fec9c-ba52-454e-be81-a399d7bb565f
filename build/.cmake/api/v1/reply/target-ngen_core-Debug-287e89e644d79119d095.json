{"artifacts": [{"path": "core/libngen_core.so"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "install", "link_directories", "target_link_libraries"], "files": ["core/CMakeLists.txt", "CMakeLists.txt", "core/messaging/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 36, "parent": 0}, {"command": 1, "file": 0, "line": 49, "parent": 0}, {"command": 1, "file": 0, "line": 50, "parent": 0}, {"file": 1}, {"command": 2, "file": 1, "line": 20, "parent": 4}, {"command": 3, "file": 0, "line": 37, "parent": 0}, {"file": 2}, {"command": 3, "file": 2, "line": 7, "parent": 7}]}, "dependencies": [{"backtrace": 6, "id": "agent_core_utilities::@a13315131675cfa87ba6"}, {"backtrace": 6, "id": "shell_cmd::@11ee51f991ec943c1614"}, {"backtrace": 6, "id": "debug_is::@a0c1859d2372bc787301"}, {"backtrace": 6, "id": "time_proxy::@b3dbbbe1cfe4be1867c6"}, {"backtrace": 6, "id": "singleton::@1e8aa1f95e3638a33048"}, {"backtrace": 6, "id": "buffers::@383b6442fe4f30043a47"}, {"backtrace": 6, "id": "mainloop::@ba6dcd28fd73657d6d1b"}, {"backtrace": 6, "id": "environment::@42a05e874c90aabce0a2"}, {"backtrace": 6, "id": "table::@c4cf692f4b54175dff2b"}, {"backtrace": 6, "id": "rest::@552c489a81002f1765c1"}, {"backtrace": 6, "id": "report::@1270cc86e0390e7112d3"}, {"backtrace": 6, "id": "logging::@97ef666c9ee1a604ead8"}, {"backtrace": 6, "id": "connkey::@2b7274ba3c33850b0cf0"}, {"backtrace": 6, "id": "messaging::@ff33175e46fc4a6269a1"}, {"backtrace": 6, "id": "messaging_comp::@8afdec6b84cf90c070f7"}, {"backtrace": 6, "id": "connection::@0c7231ed830aec82add9"}, {"backtrace": 6, "id": "messaging_buffer_comp::@e95b015eba07c5bbf346"}, {"backtrace": 6, "id": "config::@d0a6a499a33ca3bb4e51"}, {"backtrace": 6, "id": "agent_details::@f2f09c82ed669bc3ab7a"}, {"backtrace": 6, "id": "event_is::@262d1818dfec6bf12713"}, {"backtrace": 6, "id": "encryptor::@8190973107974d7ba0eb"}, {"backtrace": 6, "id": "intelligence_is_v2::@fb9a1d47fbb938d1a689"}, {"backtrace": 6, "id": "cpu::@77db136c68acb9e8e6d1"}, {"backtrace": 6, "id": "memory_consumption::@ee70a5282f86d0ec357f"}, {"backtrace": 6, "id": "instance_awareness::@5f197407e6180805be5b"}, {"backtrace": 6, "id": "socket_is::@40d9969591347f006697"}, {"backtrace": 6, "id": "agent_details_reporter::@f289bc0bfa403da8d270"}, {"backtrace": 6, "id": "metric::@ead7061abeb3290a9386"}, {"backtrace": 6, "id": "version::@a1ad84cd100617c107a5"}, {"backtrace": 6, "id": "tenant_manager::@23acacef5fb10872ff46"}, {"backtrace": 6, "id": "compression_utils::@3dcc85d11fba6ff84d38"}, {"backtrace": 6, "id": "report_messaging::@05f78eb80cce3ec23ad3"}, {"backtrace": 6, "id": "env_details::@3581fee6c10bd5aabdd0"}], "id": "ngen_core::@57760688d1f824db5d9c", "install": {"destinations": [{"backtrace": 2, "path": "lib"}, {"backtrace": 2, "path": "lib"}, {"backtrace": 3, "path": "orchestration/lib"}, {"backtrace": 3, "path": "orchestration/lib"}], "prefix": {"path": "/usr/local"}}, "link": {"commandFragments": [{"fragment": "", "role": "flags"}, {"backtrace": 5, "fragment": "-L/usr/lib/x86_64-linux-gnu/libz.so", "role": "libraryPath"}, {"fragment": "-Wl,-r<PERSON>,/usr/lib/x86_64-linux-gnu/libz.so:/home/<USER>/Code/openappsec/build/core/compression:", "role": "libraries"}, {"backtrace": 6, "fragment": "-Wl,-whole-archive", "role": "libraries"}, {"backtrace": 6, "fragment": "table/libtable.a", "role": "libraries"}, {"backtrace": 6, "fragment": "debug_is/libdebug_is.a", "role": "libraries"}, {"backtrace": 6, "fragment": "shell_cmd/libshell_cmd.a", "role": "libraries"}, {"backtrace": 6, "fragment": "metric/libmetric.a", "role": "libraries"}, {"backtrace": 6, "fragment": "tenant_manager/libtenant_manager.a", "role": "libraries"}, {"backtrace": 6, "fragment": "messaging/libmessaging.a", "role": "libraries"}, {"backtrace": 6, "fragment": "encryptor/libencryptor.a", "role": "libraries"}, {"backtrace": 6, "fragment": "time_proxy/libtime_proxy.a", "role": "libraries"}, {"backtrace": 6, "fragment": "singleton/libsingleton.a", "role": "libraries"}, {"backtrace": 6, "fragment": "mainloop/libmainloop.a", "role": "libraries"}, {"backtrace": 6, "fragment": "environment/libenvironment.a", "role": "libraries"}, {"backtrace": 6, "fragment": "logging/liblogging.a", "role": "libraries"}, {"backtrace": 6, "fragment": "report/libreport.a", "role": "libraries"}, {"backtrace": 6, "fragment": "rest/librest.a", "role": "libraries"}, {"backtrace": 6, "fragment": "compression/libcompression_utils.so", "role": "libraries"}, {"backtrace": 6, "fragment": "-lz", "role": "libraries"}, {"backtrace": 6, "fragment": "config/libconfig.a", "role": "libraries"}, {"backtrace": 6, "fragment": "intelligence_is_v2/libintelligence_is_v2.a", "role": "libraries"}, {"backtrace": 6, "fragment": "event_is/libevent_is.a", "role": "libraries"}, {"backtrace": 6, "fragment": "memory_consumption/libmemory_consumption.a", "role": "libraries"}, {"backtrace": 6, "fragment": "connkey/libconnkey.a", "role": "libraries"}, {"backtrace": 6, "fragment": "instance_awareness/libinstance_awareness.a", "role": "libraries"}, {"backtrace": 6, "fragment": "socket_is/libsocket_is.a", "role": "libraries"}, {"backtrace": 6, "fragment": "agent_details/libagent_details.a", "role": "libraries"}, {"backtrace": 6, "fragment": "agent_details_reporter/libagent_details_reporter.a", "role": "libraries"}, {"backtrace": 6, "fragment": "buffers/libbuffers.a", "role": "libraries"}, {"backtrace": 6, "fragment": "cpu/libcpu.a", "role": "libraries"}, {"backtrace": 6, "fragment": "agent_core_utilities/libagent_core_utilities.a", "role": "libraries"}, {"backtrace": 6, "fragment": "report_messaging/libreport_messaging.a", "role": "libraries"}, {"backtrace": 6, "fragment": "env_details/libenv_details.a", "role": "libraries"}, {"backtrace": 6, "fragment": "version/libversion.a", "role": "libraries"}, {"backtrace": 6, "fragment": "-Wl,-no-whole-archive", "role": "libraries"}, {"backtrace": 8, "fragment": "messaging/messaging_comp/libmessaging_comp.a", "role": "libraries"}, {"backtrace": 8, "fragment": "messaging/connection/libconnection.a", "role": "libraries"}, {"backtrace": 8, "fragment": "messaging/messaging_buffer_comp/libmessaging_buffer_comp.a", "role": "libraries"}], "language": "CXX"}, "name": "ngen_core", "nameOnDisk": "libngen_core.so", "paths": {"build": "core", "source": "core"}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "path": "core", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}