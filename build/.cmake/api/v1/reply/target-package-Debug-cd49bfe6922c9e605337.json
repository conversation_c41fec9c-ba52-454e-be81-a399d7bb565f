{"backtrace": 3, "backtraceGraph": {"commands": ["add_custom_target", "include", "add_dependencies", "gen_package"], "files": ["nodes/packaging.cmake", "nodes/CMakeLists.txt", "nodes/orchestration/CMakeLists.txt", "nodes/prometheus/CMakeLists.txt", "nodes/agent_cache/CMakeLists.txt", "nodes/http_transaction_handler/CMakeLists.txt", "nodes/attachment_registration_manager/CMakeLists.txt", "nodes/central_nginx_manager/CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 11, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 24, "parent": 2}, {"file": 2}, {"command": 3, "file": 2, "line": 178, "parent": 4}, {"command": 2, "file": 0, "line": 46, "parent": 5}, {"file": 3}, {"command": 3, "file": 3, "line": 25, "parent": 7}, {"command": 2, "file": 0, "line": 46, "parent": 8}, {"file": 4}, {"command": 3, "file": 4, "line": 3, "parent": 10}, {"command": 2, "file": 0, "line": 46, "parent": 11}, {"file": 5}, {"command": 3, "file": 5, "line": 86, "parent": 13}, {"command": 2, "file": 0, "line": 46, "parent": 14}, {"file": 6}, {"command": 3, "file": 6, "line": 25, "parent": 16}, {"command": 2, "file": 0, "line": 46, "parent": 17}, {"file": 7}, {"command": 3, "file": 7, "line": 29, "parent": 19}, {"command": 2, "file": 0, "line": 46, "parent": 20}]}, "dependencies": [{"backtrace": 6, "id": "make_package_orchestration::@b474fdd263f216d3bed2"}, {"backtrace": 9, "id": "make_package_prometheus_service::@5f1b4243bfb45cdf423f"}, {"backtrace": 12, "id": "make_package_agent_cache::@1de715c10b8abf976a47"}, {"backtrace": 15, "id": "make_package_http_transaction_handler_service::@6a2d719aa39f4aee4b1d"}, {"backtrace": 18, "id": "make_package_attachment_registration_manager_service::@5599c499f1a018a19a5d"}, {"backtrace": 21, "id": "make_package_central_nginx_manager::@2138b9ab414ffd11efe3"}], "id": "package::@5e780104c5aa35846c34", "name": "package", "paths": {"build": "nodes", "source": "nodes"}, "sources": [], "type": "UTILITY"}