{"archive": {}, "artifacts": [{"path": "components/security_apps/orchestration/updates_process_reporter/libupdates_process_reporter.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "include_directories"], "files": ["components/security_apps/orchestration/updates_process_reporter/CMakeLists.txt", "CMakeLists.txt", "components/security_apps/orchestration/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 1, "parent": 0}, {"file": 1}, {"command": 1, "file": 1, "line": 21, "parent": 2}, {"command": 1, "file": 1, "line": 22, "parent": 2}, {"command": 1, "file": 1, "line": 26, "parent": 2}, {"command": 1, "file": 1, "line": 27, "parent": 2}, {"command": 1, "file": 1, "line": 28, "parent": 2}, {"command": 1, "file": 1, "line": 29, "parent": 2}, {"command": 1, "file": 1, "line": 30, "parent": 2}, {"command": 1, "file": 1, "line": 31, "parent": 2}, {"command": 1, "file": 1, "line": 32, "parent": 2}, {"command": 1, "file": 1, "line": 33, "parent": 2}, {"command": 1, "file": 1, "line": 34, "parent": 2}, {"command": 1, "file": 1, "line": 35, "parent": 2}, {"command": 1, "file": 1, "line": 36, "parent": 2}, {"command": 1, "file": 1, "line": 37, "parent": 2}, {"file": 2}, {"command": 1, "file": 2, "line": 3, "parent": 17}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -O2 -fPIC -Wall -Wno-terminate -g"}], "defines": [{"define": "USERSPACE"}], "includes": [{"backtrace": 3, "path": "/usr/include/libxml2"}, {"backtrace": 4, "path": "/usr/src/googletest/googlemock/include"}, {"backtrace": 5, "path": "/home/<USER>/Code/openappsec/external"}, {"backtrace": 6, "path": "/home/<USER>/Code/openappsec/external/yajl/yajl-2.1.1/include"}, {"backtrace": 7, "path": "/home/<USER>/Code/openappsec/external/C-Mock/include/cmock"}, {"backtrace": 8, "path": "/home/<USER>/Code/openappsec/external/picojson"}, {"backtrace": 9, "path": "/home/<USER>/Code/openappsec/core/include/general"}, {"backtrace": 10, "path": "/home/<USER>/Code/openappsec/core/include/internal"}, {"backtrace": 11, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/interfaces"}, {"backtrace": 12, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/resources"}, {"backtrace": 13, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/utilities"}, {"backtrace": 14, "path": "/home/<USER>/Code/openappsec/core/include/attachments"}, {"backtrace": 15, "path": "/home/<USER>/Code/openappsec/events/include"}, {"backtrace": 16, "path": "/home/<USER>/Code/openappsec/components/include"}, {"backtrace": 18, "path": "/home/<USER>/Code/openappsec/components/security_apps/orchestration/include"}], "language": "CXX", "sourceIndexes": [0, 1]}], "id": "updates_process_reporter::@aeae672129fa1cd73ab5", "name": "updates_process_reporter", "nameOnDisk": "libupdates_process_reporter.a", "paths": {"build": "components/security_apps/orchestration/updates_process_reporter", "source": "components/security_apps/orchestration/updates_process_reporter"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/orchestration/updates_process_reporter/updates_process_event.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/orchestration/updates_process_reporter/updates_process_reporter.cc", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}