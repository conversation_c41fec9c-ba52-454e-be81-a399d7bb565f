{"archive": {}, "artifacts": [{"path": "core/version/libversion.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "include_directories", "target_include_directories"], "files": ["core/version/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 10, "parent": 0}, {"file": 1}, {"command": 1, "file": 1, "line": 21, "parent": 2}, {"command": 1, "file": 1, "line": 22, "parent": 2}, {"command": 1, "file": 1, "line": 26, "parent": 2}, {"command": 1, "file": 1, "line": 27, "parent": 2}, {"command": 1, "file": 1, "line": 28, "parent": 2}, {"command": 1, "file": 1, "line": 29, "parent": 2}, {"command": 1, "file": 1, "line": 30, "parent": 2}, {"command": 1, "file": 1, "line": 31, "parent": 2}, {"command": 1, "file": 1, "line": 32, "parent": 2}, {"command": 1, "file": 1, "line": 33, "parent": 2}, {"command": 1, "file": 1, "line": 34, "parent": 2}, {"command": 1, "file": 1, "line": 35, "parent": 2}, {"command": 1, "file": 1, "line": 36, "parent": 2}, {"command": 1, "file": 1, "line": 37, "parent": 2}, {"command": 2, "file": 0, "line": 11, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -O2 -fPIC -Wall -Wno-terminate -g"}], "includes": [{"backtrace": 3, "path": "/usr/include/libxml2"}, {"backtrace": 4, "path": "/usr/src/googletest/googlemock/include"}, {"backtrace": 5, "path": "/home/<USER>/Code/openappsec/external"}, {"backtrace": 6, "path": "/home/<USER>/Code/openappsec/external/yajl/yajl-2.1.1/include"}, {"backtrace": 7, "path": "/home/<USER>/Code/openappsec/external/C-Mock/include/cmock"}, {"backtrace": 8, "path": "/home/<USER>/Code/openappsec/external/picojson"}, {"backtrace": 9, "path": "/home/<USER>/Code/openappsec/core/include/general"}, {"backtrace": 10, "path": "/home/<USER>/Code/openappsec/core/include/internal"}, {"backtrace": 11, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/interfaces"}, {"backtrace": 12, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/resources"}, {"backtrace": 13, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/utilities"}, {"backtrace": 14, "path": "/home/<USER>/Code/openappsec/core/include/attachments"}, {"backtrace": 15, "path": "/home/<USER>/Code/openappsec/events/include"}, {"backtrace": 16, "path": "/home/<USER>/Code/openappsec/components/include"}, {"backtrace": 17, "path": "/home/<USER>/Code/openappsec/build/core/version"}], "language": "CXX", "sourceIndexes": [0]}], "id": "version::@a1ad84cd100617c107a5", "name": "version", "nameOnDisk": "libversion.a", "paths": {"build": "core/version", "source": "core/version"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1]}, {"name": "CMake Rules", "sourceIndexes": [2]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "core/version/version.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "isGenerated": true, "path": "build/core/version/version_vars.h", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/core/version/version_vars.h.rule", "sourceGroupIndex": 2}], "type": "STATIC_LIBRARY"}