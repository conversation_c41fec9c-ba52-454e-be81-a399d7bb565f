{"backtrace": 2, "backtraceGraph": {"commands": ["add_custom_target", "gen_package"], "files": ["nodes/packaging.cmake", "nodes/prometheus/CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 25, "parent": 0}, {"command": 0, "file": 0, "line": 45, "parent": 1}]}, "id": "make_package_prometheus_service::@5f1b4243bfb45cdf423f", "name": "make_package_prometheus_service", "paths": {"build": "nodes/prometheus", "source": "nodes/prometheus"}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1, 2]}], "sources": [{"backtrace": 2, "isGenerated": true, "path": "build/nodes/prometheus/CMakeFiles/make_package_prometheus_service", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/nodes/prometheus/CMakeFiles/make_package_prometheus_service.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "/usr/local/install-cp-nano-service-prometheus.sh.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}