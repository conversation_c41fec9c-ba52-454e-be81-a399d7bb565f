{"backtrace": 2, "backtraceGraph": {"commands": ["add_custom_target", "gen_package"], "files": ["nodes/packaging.cmake", "nodes/http_transaction_handler/CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 86, "parent": 0}, {"command": 0, "file": 0, "line": 45, "parent": 1}]}, "id": "make_package_http_transaction_handler_service::@6a2d719aa39f4aee4b1d", "name": "make_package_http_transaction_handler_service", "paths": {"build": "nodes/http_transaction_handler", "source": "nodes/http_transaction_handler"}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1, 2]}], "sources": [{"backtrace": 2, "isGenerated": true, "path": "build/nodes/http_transaction_handler/CMakeFiles/make_package_http_transaction_handler_service", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/nodes/http_transaction_handler/CMakeFiles/make_package_http_transaction_handler_service.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "/usr/local/install-cp-nano-service-http-transaction-handler.sh.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}