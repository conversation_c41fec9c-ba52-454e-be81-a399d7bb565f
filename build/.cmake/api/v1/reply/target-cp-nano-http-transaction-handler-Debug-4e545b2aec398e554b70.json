{"artifacts": [{"path": "nodes/http_transaction_handler/cp-nano-http-transaction-handler"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "install", "link_directories", "target_link_libraries", "add_dependencies", "include_directories"], "files": ["nodes/http_transaction_handler/CMakeLists.txt", "CMakeLists.txt", "nodes/CMakeLists.txt", "components/attachment-intakers/nginx_attachment/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 6, "parent": 0}, {"command": 1, "file": 0, "line": 49, "parent": 0}, {"command": 1, "file": 0, "line": 50, "parent": 0}, {"file": 1}, {"command": 2, "file": 1, "line": 20, "parent": 4}, {"file": 2}, {"command": 2, "file": 2, "line": 6, "parent": 6}, {"command": 2, "file": 2, "line": 7, "parent": 6}, {"command": 2, "file": 0, "line": 3, "parent": 0}, {"command": 2, "file": 0, "line": 4, "parent": 0}, {"command": 3, "file": 0, "line": 8, "parent": 0}, {"file": 3}, {"command": 3, "file": 3, "line": 5, "parent": 12}, {"command": 4, "file": 0, "line": 47, "parent": 0}, {"command": 5, "file": 1, "line": 21, "parent": 4}, {"command": 5, "file": 1, "line": 22, "parent": 4}, {"command": 5, "file": 1, "line": 26, "parent": 4}, {"command": 5, "file": 1, "line": 27, "parent": 4}, {"command": 5, "file": 1, "line": 28, "parent": 4}, {"command": 5, "file": 1, "line": 29, "parent": 4}, {"command": 5, "file": 1, "line": 30, "parent": 4}, {"command": 5, "file": 1, "line": 31, "parent": 4}, {"command": 5, "file": 1, "line": 32, "parent": 4}, {"command": 5, "file": 1, "line": 33, "parent": 4}, {"command": 5, "file": 1, "line": 34, "parent": 4}, {"command": 5, "file": 1, "line": 35, "parent": 4}, {"command": 5, "file": 1, "line": 36, "parent": 4}, {"command": 5, "file": 1, "line": 37, "parent": 4}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -O2 -fPIC -Wall -Wno-terminate -g"}], "includes": [{"backtrace": 15, "path": "/usr/include/libxml2"}, {"backtrace": 16, "path": "/usr/src/googletest/googlemock/include"}, {"backtrace": 17, "path": "/home/<USER>/Code/openappsec/external"}, {"backtrace": 18, "path": "/home/<USER>/Code/openappsec/external/yajl/yajl-2.1.1/include"}, {"backtrace": 19, "path": "/home/<USER>/Code/openappsec/external/C-Mock/include/cmock"}, {"backtrace": 20, "path": "/home/<USER>/Code/openappsec/external/picojson"}, {"backtrace": 21, "path": "/home/<USER>/Code/openappsec/core/include/general"}, {"backtrace": 22, "path": "/home/<USER>/Code/openappsec/core/include/internal"}, {"backtrace": 23, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/interfaces"}, {"backtrace": 24, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/resources"}, {"backtrace": 25, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/utilities"}, {"backtrace": 26, "path": "/home/<USER>/Code/openappsec/core/include/attachments"}, {"backtrace": 27, "path": "/home/<USER>/Code/openappsec/events/include"}, {"backtrace": 28, "path": "/home/<USER>/Code/openappsec/components/include"}], "language": "CXX", "sourceIndexes": [0]}], "dependencies": [{"backtrace": 11, "id": "graphqlparser::@b5ec2cb062faa4171e82"}, {"backtrace": 11, "id": "yajl_s::@971c39514bf700832d90"}, {"backtrace": 14, "id": "ngen_core::@57760688d1f824db5d9c"}, {"backtrace": 11, "id": "buffers::@383b6442fe4f30043a47"}, {"backtrace": 11, "id": "table::@c4cf692f4b54175dff2b"}, {"backtrace": 11, "id": "connkey::@2b7274ba3c33850b0cf0"}, {"backtrace": 11, "id": "version::@a1ad84cd100617c107a5"}, {"backtrace": 11, "id": "http_configuration::@93db0a132eac751b282e"}, {"backtrace": 11, "id": "report_messaging::@05f78eb80cce3ec23ad3"}, {"backtrace": 11, "id": "http_manager_comp::@9e70760e38f11c13e9d3"}, {"backtrace": 11, "id": "signal_handler::@06657768f40ddbd8cb85"}, {"backtrace": 11, "id": "gradual_deployment::@9898ddc6be0d9c4fe05c"}, {"backtrace": 11, "id": "generic_rulebase::@bb67be512c8681bc130b"}, {"backtrace": 11, "id": "generic_rulebase_evaluators::@68906a8a6cd19d6f7ace"}, {"backtrace": 11, "id": "geo_location::@4ec0465ac7a274351a09"}, {"backtrace": 11, "id": "http_transaction_data::@22977122975000546c97"}, {"backtrace": 11, "id": "ip_utilities::@ccbabc50234818a45efb"}, {"backtrace": 11, "id": "keywords::@8a47f11b85238419b0e0"}, {"backtrace": 11, "id": "pm::@4ec627517e02553c20c2"}, {"backtrace": 11, "id": "nginx_attachment::@9a35884d437d9fc337f1"}, {"backtrace": 11, "id": "http_geo_filter::@1b553477a90d45a320d2"}, {"backtrace": 11, "id": "ips::@b375cdc84c2bb938f29f"}, {"backtrace": 11, "id": "l7_access_control::@dd4008c99f1b04afd4c0"}, {"backtrace": 11, "id": "rate_limit_comp::@ccc459f56a5cada2373c"}, {"backtrace": 11, "id": "rate_limit_config::@ccc459f56a5cada2373c"}, {"backtrace": 11, "id": "waap::@2adc4b79945368266c4d"}, {"backtrace": 11, "id": "waap_clib::@7c8cd5ce64b722c32cbf"}, {"backtrace": 11, "id": "reputation::@f7cad0ed6a73ef3969fc"}], "id": "cp-nano-http-transaction-handler::@6a2d719aa39f4aee4b1d", "install": {"destinations": [{"backtrace": 2, "path": "bin"}, {"backtrace": 3, "path": "http_transaction_handler_service/bin"}], "prefix": {"path": "/usr/local"}}, "link": {"commandFragments": [{"fragment": "-O2 -fPIC -Wall -Wno-terminate -g", "role": "flags"}, {"fragment": "-rdynamic", "role": "flags"}, {"backtrace": 5, "fragment": "-L/usr/lib/x86_64-linux-gnu/libz.so", "role": "libraryPath"}, {"backtrace": 7, "fragment": "-L/home/<USER>/Code/openappsec/build/core", "role": "libraryPath"}, {"backtrace": 8, "fragment": "-L/home/<USER>/Code/openappsec/build/core/compression", "role": "libraryPath"}, {"backtrace": 9, "fragment": "-L/home/<USER>/Code/openappsec/build/core/shmem_ipc", "role": "libraryPath"}, {"backtrace": 10, "fragment": "-L/home/<USER>/Code/openappsec/build/attachments/nginx/nginx_attachment_util", "role": "libraryPath"}, {"fragment": "-Wl,-rpath,/usr/lib/x86_64-linux-gnu/libz.so:/home/<USER>/Code/openappsec/build/core:/home/<USER>/Code/openappsec/build/core/compression:/home/<USER>/Code/openappsec/build/core/shmem_ipc:/home/<USER>/Code/openappsec/build/attachments/nginx/nginx_attachment_util:", "role": "libraries"}, {"backtrace": 11, "fragment": "-Wl,--start-group", "role": "libraries"}, {"backtrace": 11, "fragment": "-lngen_core", "role": "libraries"}, {"backtrace": 11, "fragment": "-lcompression_utils", "role": "libraries"}, {"backtrace": 11, "fragment": "-lssl", "role": "libraries"}, {"backtrace": 11, "fragment": "-lcrypto", "role": "libraries"}, {"backtrace": 11, "fragment": "-lz", "role": "libraries"}, {"backtrace": 11, "fragment": "-lboost_context", "role": "libraries"}, {"backtrace": 11, "fragment": "-lboost_atomic", "role": "libraries"}, {"backtrace": 11, "fragment": "-lboost_regex", "role": "libraries"}, {"backtrace": 11, "fragment": "-lboost_filesystem", "role": "libraries"}, {"backtrace": 11, "fragment": "-lboost_system", "role": "libraries"}, {"backtrace": 11, "fragment": "-l<PERSON><PERSON><PERSON>", "role": "libraries"}, {"backtrace": 11, "fragment": "../../external/graphqlparser/libgraphqlparser.a", "role": "libraries"}, {"backtrace": 11, "fragment": "-lxml2", "role": "libraries"}, {"backtrace": 11, "fragment": "-lpcre2-8", "role": "libraries"}, {"backtrace": 11, "fragment": "-lpcre2-posix", "role": "libraries"}, {"backtrace": 11, "fragment": "../../external/yajl/yajl-2.1.1/lib/libyajl_s.a", "role": "libraries"}, {"backtrace": 11, "fragment": "-l<PERSON><PERSON>", "role": "libraries"}, {"backtrace": 11, "fragment": "-lmaxminddb", "role": "libraries"}, {"backtrace": 11, "fragment": "-lshmem_ipc", "role": "libraries"}, {"backtrace": 11, "fragment": "-lnginx_attachment_util", "role": "libraries"}, {"backtrace": 11, "fragment": "../../components/utils/generic_rulebase/libgeneric_rulebase.a", "role": "libraries"}, {"backtrace": 11, "fragment": "../../components/utils/generic_rulebase/evaluators/libgeneric_rulebase_evaluators.a", "role": "libraries"}, {"backtrace": 11, "fragment": "../../components/utils/ip_utilities/libip_utilities.a", "role": "libraries"}, {"backtrace": 11, "fragment": "../../core/version/libversion.a", "role": "libraries"}, {"backtrace": 11, "fragment": "../../components/signal_handler/libsignal_handler.a", "role": "libraries"}, {"backtrace": 11, "fragment": "../../core/report_messaging/libreport_messaging.a", "role": "libraries"}, {"backtrace": 11, "fragment": "../../components/attachment-intakers/nginx_attachment/libnginx_attachment.a", "role": "libraries"}, {"backtrace": 11, "fragment": "../../components/gradual_deployment/libgradual_deployment.a", "role": "libraries"}, {"backtrace": 11, "fragment": "../../components/http_manager/libhttp_manager_comp.a", "role": "libraries"}, {"backtrace": 11, "fragment": "../../components/utils/pm/libpm.a", "role": "libraries"}, {"backtrace": 11, "fragment": "../../components/security_apps/waap/libwaap.a", "role": "libraries"}, {"backtrace": 11, "fragment": "../../components/security_apps/waap/waap_clib/libwaap_clib.a", "role": "libraries"}, {"backtrace": 11, "fragment": "../../components/security_apps/waap/reputation/libreputation.a", "role": "libraries"}, {"backtrace": 11, "fragment": "../../components/security_apps/rate_limit/librate_limit_comp.a", "role": "libraries"}, {"backtrace": 11, "fragment": "../../components/security_apps/rate_limit/librate_limit_config.a", "role": "libraries"}, {"backtrace": 11, "fragment": "../../components/security_apps/ips/libips.a", "role": "libraries"}, {"backtrace": 11, "fragment": "../../components/utils/keywords/libkeywords.a", "role": "libraries"}, {"backtrace": 11, "fragment": "../../components/security_apps/layer_7_access_control/libl7_access_control.a", "role": "libraries"}, {"backtrace": 11, "fragment": "../../components/utils/geo_location/libgeo_location.a", "role": "libraries"}, {"backtrace": 11, "fragment": "../../components/security_apps/http_geo_filter/libhttp_geo_filter.a", "role": "libraries"}, {"backtrace": 11, "fragment": "-Wl,--end-group", "role": "libraries"}, {"backtrace": 11, "fragment": "-lshmem_ipc", "role": "libraries"}, {"backtrace": 13, "fragment": "../../core/attachments/http_configuration/libhttp_configuration.a", "role": "libraries"}, {"backtrace": 13, "fragment": "../../components/utils/http_transaction_data/libhttp_transaction_data.a", "role": "libraries"}, {"backtrace": 13, "fragment": "../../core/connkey/libconnkey.a", "role": "libraries"}, {"backtrace": 13, "fragment": "../../core/table/libtable.a", "role": "libraries"}, {"backtrace": 13, "fragment": "../../core/buffers/libbuffers.a", "role": "libraries"}], "language": "CXX"}, "name": "cp-nano-http-transaction-handler", "nameOnDisk": "cp-nano-http-transaction-handler", "paths": {"build": "nodes/http_transaction_handler", "source": "nodes/http_transaction_handler"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "nodes/http_transaction_handler/main.cc", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}