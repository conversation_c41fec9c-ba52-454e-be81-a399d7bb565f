{"backtraceGraph": {"commands": ["install"], "files": ["nodes/attachment_registration_manager/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 22, "parent": 0}, {"command": 0, "file": 0, "line": 23, "parent": 0}]}, "installers": [{"backtrace": 1, "component": "Unspecified", "destination": "bin", "paths": ["nodes/attachment_registration_manager/attachment_registration_manager"], "targetId": "attachment_registration_manager::@5599c499f1a018a19a5d", "targetIndex": 6, "type": "target"}, {"backtrace": 1, "component": "Unspecified", "cxxModuleBmiTarget": {"id": "attachment_registration_manager::@5599c499f1a018a19a5d", "index": 6}, "destination": "bin", "type": "cxxModuleBmi"}, {"backtrace": 2, "component": "Unspecified", "destination": "attachment_registration_manager_service/bin", "paths": ["nodes/attachment_registration_manager/attachment_registration_manager"], "targetId": "attachment_registration_manager::@5599c499f1a018a19a5d", "targetIndex": 6, "type": "target"}, {"backtrace": 2, "component": "Unspecified", "cxxModuleBmiTarget": {"id": "attachment_registration_manager::@5599c499f1a018a19a5d", "index": 6}, "destination": "attachment_registration_manager_service/bin", "type": "cxxModuleBmi"}], "paths": {"build": "nodes/attachment_registration_manager", "source": "nodes/attachment_registration_manager"}}