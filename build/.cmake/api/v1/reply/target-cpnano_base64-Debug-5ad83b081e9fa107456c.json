{"artifacts": [{"path": "core/encryptor/cpnano_base64/cpnano_base64"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "install", "link_directories", "include_directories"], "files": ["core/encryptor/cpnano_base64/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 1, "parent": 0}, {"command": 1, "file": 0, "line": 5, "parent": 0}, {"command": 1, "file": 0, "line": 6, "parent": 0}, {"file": 1}, {"command": 2, "file": 1, "line": 20, "parent": 4}, {"command": 3, "file": 1, "line": 21, "parent": 4}, {"command": 3, "file": 1, "line": 22, "parent": 4}, {"command": 3, "file": 1, "line": 26, "parent": 4}, {"command": 3, "file": 1, "line": 27, "parent": 4}, {"command": 3, "file": 1, "line": 28, "parent": 4}, {"command": 3, "file": 1, "line": 29, "parent": 4}, {"command": 3, "file": 1, "line": 30, "parent": 4}, {"command": 3, "file": 1, "line": 31, "parent": 4}, {"command": 3, "file": 1, "line": 32, "parent": 4}, {"command": 3, "file": 1, "line": 33, "parent": 4}, {"command": 3, "file": 1, "line": 34, "parent": 4}, {"command": 3, "file": 1, "line": 35, "parent": 4}, {"command": 3, "file": 1, "line": 36, "parent": 4}, {"command": 3, "file": 1, "line": 37, "parent": 4}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -O2 -fPIC -Wall -Wno-terminate  -g"}], "includes": [{"backtrace": 6, "path": "/usr/include/libxml2"}, {"backtrace": 7, "path": "/usr/src/googletest/googlemock/include"}, {"backtrace": 8, "path": "/home/<USER>/Code/openappsec/external"}, {"backtrace": 9, "path": "/home/<USER>/Code/openappsec/external/yajl/yajl-2.1.1/include"}, {"backtrace": 10, "path": "/home/<USER>/Code/openappsec/external/C-Mock/include/cmock"}, {"backtrace": 11, "path": "/home/<USER>/Code/openappsec/external/picojson"}, {"backtrace": 12, "path": "/home/<USER>/Code/openappsec/core/include/general"}, {"backtrace": 13, "path": "/home/<USER>/Code/openappsec/core/include/internal"}, {"backtrace": 14, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/interfaces"}, {"backtrace": 15, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/resources"}, {"backtrace": 16, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/utilities"}, {"backtrace": 17, "path": "/home/<USER>/Code/openappsec/core/include/attachments"}, {"backtrace": 18, "path": "/home/<USER>/Code/openappsec/events/include"}, {"backtrace": 19, "path": "/home/<USER>/Code/openappsec/components/include"}], "language": "CXX", "sourceIndexes": [0, 1]}], "id": "cpnano_base64::@54c975be1dba3c8d2a85", "install": {"destinations": [{"backtrace": 2, "path": "bin"}, {"backtrace": 3, "path": "orchestration"}], "prefix": {"path": "/usr/local"}}, "link": {"commandFragments": [{"fragment": "-O2 -fPIC -Wall -Wno-terminate  -g", "role": "flags"}, {"fragment": "-rdynamic", "role": "flags"}, {"backtrace": 5, "fragment": "-L/usr/lib/x86_64-linux-gnu/libz.so", "role": "libraryPath"}], "language": "CXX"}, "name": "cpnano_base64", "nameOnDisk": "cpnano_base64", "paths": {"build": "core/encryptor/cpnano_base64", "source": "core/encryptor/cpnano_base64"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "core/encryptor/cpnano_base64/cpnano_base64.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "core/encryptor/cpnano_base64/base64.cc", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}