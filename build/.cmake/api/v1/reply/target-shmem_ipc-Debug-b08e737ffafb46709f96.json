{"artifacts": [{"path": "core/shmem_ipc/libshmem_ipc.so"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "install", "link_directories", "target_link_libraries", "include_directories"], "files": ["core/shmem_ipc/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 3, "parent": 0}, {"command": 1, "file": 0, "line": 9, "parent": 0}, {"command": 1, "file": 0, "line": 10, "parent": 0}, {"file": 1}, {"command": 2, "file": 1, "line": 20, "parent": 4}, {"command": 3, "file": 0, "line": 5, "parent": 0}, {"command": 4, "file": 1, "line": 21, "parent": 4}, {"command": 4, "file": 1, "line": 22, "parent": 4}, {"command": 4, "file": 1, "line": 26, "parent": 4}, {"command": 4, "file": 1, "line": 27, "parent": 4}, {"command": 4, "file": 1, "line": 28, "parent": 4}, {"command": 4, "file": 1, "line": 29, "parent": 4}, {"command": 4, "file": 1, "line": 30, "parent": 4}, {"command": 4, "file": 1, "line": 31, "parent": 4}, {"command": 4, "file": 1, "line": 32, "parent": 4}, {"command": 4, "file": 1, "line": 33, "parent": 4}, {"command": 4, "file": 1, "line": 34, "parent": 4}, {"command": 4, "file": 1, "line": 35, "parent": 4}, {"command": 4, "file": 1, "line": 36, "parent": 4}, {"command": 4, "file": 1, "line": 37, "parent": 4}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -fPIC"}], "defines": [{"define": "shmem_ipc_EXPORTS"}], "includes": [{"backtrace": 7, "path": "/usr/include/libxml2"}, {"backtrace": 8, "path": "/usr/src/googletest/googlemock/include"}, {"backtrace": 9, "path": "/home/<USER>/Code/openappsec/external"}, {"backtrace": 10, "path": "/home/<USER>/Code/openappsec/external/yajl/yajl-2.1.1/include"}, {"backtrace": 11, "path": "/home/<USER>/Code/openappsec/external/C-Mock/include/cmock"}, {"backtrace": 12, "path": "/home/<USER>/Code/openappsec/external/picojson"}, {"backtrace": 13, "path": "/home/<USER>/Code/openappsec/core/include/general"}, {"backtrace": 14, "path": "/home/<USER>/Code/openappsec/core/include/internal"}, {"backtrace": 15, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/interfaces"}, {"backtrace": 16, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/resources"}, {"backtrace": 17, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/utilities"}, {"backtrace": 18, "path": "/home/<USER>/Code/openappsec/core/include/attachments"}, {"backtrace": 19, "path": "/home/<USER>/Code/openappsec/events/include"}, {"backtrace": 20, "path": "/home/<USER>/Code/openappsec/components/include"}], "language": "C", "sourceIndexes": [0, 1]}], "id": "shmem_ipc::@5a899c60f4d86b83914f", "install": {"destinations": [{"backtrace": 2, "path": "lib"}, {"backtrace": 2, "path": "lib"}, {"backtrace": 3, "path": "http_transaction_handler_service/lib"}, {"backtrace": 3, "path": "http_transaction_handler_service/lib"}], "prefix": {"path": "/usr/local"}}, "link": {"commandFragments": [{"fragment": "", "role": "flags"}, {"backtrace": 5, "fragment": "-L/usr/lib/x86_64-linux-gnu/libz.so", "role": "libraryPath"}, {"fragment": "-Wl,-r<PERSON>,/usr/lib/x86_64-linux-gnu/libz.so:", "role": "libraries"}, {"backtrace": 6, "fragment": "-lrt", "role": "libraries"}], "language": "C"}, "name": "shmem_ipc", "nameOnDisk": "libshmem_ipc.so", "paths": {"build": "core/shmem_ipc", "source": "core/shmem_ipc"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "core/shmem_ipc/shmem_ipc.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "core/shmem_ipc/shared_ring_queue.c", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}