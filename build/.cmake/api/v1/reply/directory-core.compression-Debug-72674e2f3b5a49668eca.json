{"backtraceGraph": {"commands": ["install"], "files": ["core/compression/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 9, "parent": 0}, {"command": 0, "file": 0, "line": 10, "parent": 0}, {"command": 0, "file": 0, "line": 12, "parent": 0}]}, "installers": [{"backtrace": 1, "component": "Unspecified", "destination": "lib", "paths": ["core/compression/libcompression_utils.so"], "targetId": "compression_utils::@3dcc85d11fba6ff84d38", "targetIndex": 12, "type": "target"}, {"backtrace": 1, "component": "Unspecified", "cxxModuleBmiTarget": {"id": "compression_utils::@3dcc85d11fba6ff84d38", "index": 12}, "destination": "lib", "type": "cxxModuleBmi"}, {"backtrace": 2, "component": "Unspecified", "destination": "http_transaction_handler_service/lib", "paths": ["core/compression/libcompression_utils.so"], "targetId": "compression_utils::@3dcc85d11fba6ff84d38", "targetIndex": 12, "type": "target"}, {"backtrace": 2, "component": "Unspecified", "cxxModuleBmiTarget": {"id": "compression_utils::@3dcc85d11fba6ff84d38", "index": 12}, "destination": "http_transaction_handler_service/lib", "type": "cxxModuleBmi"}, {"backtrace": 3, "component": "Unspecified", "destination": "lib", "paths": ["core/compression/libstatic_compression_utils.a"], "targetId": "static_compression_utils::@3dcc85d11fba6ff84d38", "targetIndex": 134, "type": "target"}, {"backtrace": 3, "component": "Unspecified", "cxxModuleBmiTarget": {"id": "static_compression_utils::@3dcc85d11fba6ff84d38", "index": 134}, "destination": "lib", "type": "cxxModuleBmi"}], "paths": {"build": "core/compression", "source": "core/compression"}}