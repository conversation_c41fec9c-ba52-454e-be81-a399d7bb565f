{"artifacts": [{"path": "core/shm_pkt_queue/shm_pkt_queue_ut/shm_pkt_queue_ut"}], "backtrace": 2, "backtraceGraph": {"commands": ["add_executable", "add_unit_test", "link_directories", "target_link_libraries", "include_directories"], "files": ["unit_test.cmake", "core/shm_pkt_queue/shm_pkt_queue_ut/CMakeLists.txt", "CMakeLists.txt", "core/shm_pkt_queue/CMakeLists.txt", "core/config/CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 5, "parent": 0}, {"command": 0, "file": 0, "line": 4, "parent": 1}, {"file": 2}, {"command": 2, "file": 2, "line": 20, "parent": 3}, {"file": 3}, {"command": 2, "file": 3, "line": 6, "parent": 5}, {"command": 3, "file": 0, "line": 5, "parent": 1}, {"command": 3, "file": 3, "line": 9, "parent": 5}, {"file": 4}, {"command": 3, "file": 4, "line": 2, "parent": 9}, {"command": 4, "file": 2, "line": 21, "parent": 3}, {"command": 4, "file": 2, "line": 22, "parent": 3}, {"command": 4, "file": 2, "line": 26, "parent": 3}, {"command": 4, "file": 2, "line": 27, "parent": 3}, {"command": 4, "file": 2, "line": 28, "parent": 3}, {"command": 4, "file": 2, "line": 29, "parent": 3}, {"command": 4, "file": 2, "line": 30, "parent": 3}, {"command": 4, "file": 2, "line": 31, "parent": 3}, {"command": 4, "file": 2, "line": 32, "parent": 3}, {"command": 4, "file": 2, "line": 33, "parent": 3}, {"command": 4, "file": 2, "line": 34, "parent": 3}, {"command": 4, "file": 2, "line": 35, "parent": 3}, {"command": 4, "file": 2, "line": 36, "parent": 3}, {"command": 4, "file": 2, "line": 37, "parent": 3}, {"command": 4, "file": 1, "line": 2, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -O2 -fPIC -Wall -Wno-terminate -g   -Wno-strict-aliasing -Wno-class-memaccess -Wno-maybe-uninitialized"}, {"backtrace": 7, "fragment": "-DGTEST_HAS_PTHREAD=1"}], "includes": [{"backtrace": 11, "path": "/usr/include/libxml2"}, {"backtrace": 12, "path": "/usr/src/googletest/googlemock/include"}, {"backtrace": 13, "path": "/home/<USER>/Code/openappsec/external"}, {"backtrace": 14, "path": "/home/<USER>/Code/openappsec/external/yajl/yajl-2.1.1/include"}, {"backtrace": 15, "path": "/home/<USER>/Code/openappsec/external/C-Mock/include/cmock"}, {"backtrace": 16, "path": "/home/<USER>/Code/openappsec/external/picojson"}, {"backtrace": 17, "path": "/home/<USER>/Code/openappsec/core/include/general"}, {"backtrace": 18, "path": "/home/<USER>/Code/openappsec/core/include/internal"}, {"backtrace": 19, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/interfaces"}, {"backtrace": 20, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/resources"}, {"backtrace": 21, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/utilities"}, {"backtrace": 22, "path": "/home/<USER>/Code/openappsec/core/include/attachments"}, {"backtrace": 23, "path": "/home/<USER>/Code/openappsec/events/include"}, {"backtrace": 24, "path": "/home/<USER>/Code/openappsec/components/include"}, {"backtrace": 25, "path": "/home/<USER>/Code/openappsec/fdio_plugin/shm_pkt_queue/include"}], "language": "CXX", "sourceIndexes": [0]}], "dependencies": [{"backtrace": 7, "id": "cptest::@0c5334b3e4869b117d9f"}, {"backtrace": 7, "id": "agent_core_utilities::@a13315131675cfa87ba6"}, {"backtrace": 7, "id": "debug_is::@a0c1859d2372bc787301"}, {"backtrace": 7, "id": "singleton::@1e8aa1f95e3638a33048"}, {"backtrace": 7, "id": "buffers::@383b6442fe4f30043a47"}, {"backtrace": 7, "id": "environment::@42a05e874c90aabce0a2"}, {"backtrace": 7, "id": "rest::@552c489a81002f1765c1"}, {"backtrace": 7, "id": "report::@1270cc86e0390e7112d3"}, {"backtrace": 7, "id": "config::@d0a6a499a33ca3bb4e51"}, {"backtrace": 7, "id": "event_is::@262d1818dfec6bf12713"}, {"backtrace": 7, "id": "shm_pkt_queue::@a805dd128f5d38de9bfb"}, {"backtrace": 7, "id": "metric::@ead7061abeb3290a9386"}, {"backtrace": 7, "id": "version::@a1ad84cd100617c107a5"}, {"backtrace": 7, "id": "compression_utils::@3dcc85d11fba6ff84d38"}, {"backtrace": 7, "id": "packet::@db2bc6f12dc570b4bd47"}], "id": "shm_pkt_queue_ut::@a8f2562dd85f7cbaa775", "link": {"commandFragments": [{"fragment": "-O2 -fPIC -Wall -Wno-terminate -g", "role": "flags"}, {"fragment": "-rdynamic", "role": "flags"}, {"backtrace": 4, "fragment": "-L/usr/lib/x86_64-linux-gnu/libz.so", "role": "libraryPath"}, {"backtrace": 6, "fragment": "-L/nginx_cache_shmem", "role": "libraryPath"}, {"fragment": "-Wl,-rpath,/usr/lib/x86_64-linux-gnu/libz.so:/nginx_cache_shmem:/home/<USER>/Code/openappsec/build/core/shm_pkt_queue:/home/<USER>/Code/openappsec/build/core/compression", "role": "libraries"}, {"backtrace": 7, "fragment": "-Wl,--start-group", "role": "libraries"}, {"backtrace": 7, "fragment": "../../buffers/libbuffers.a", "role": "libraries"}, {"backtrace": 7, "fragment": "../libshm_pkt_queue.so", "role": "libraries"}, {"backtrace": 7, "fragment": "../../version/libversion.a", "role": "libraries"}, {"backtrace": 7, "fragment": "../../debug_is/libdebug_is.a", "role": "libraries"}, {"backtrace": 7, "fragment": "../../report/libreport.a", "role": "libraries"}, {"backtrace": 7, "fragment": "../../cptest/libcptest.a", "role": "libraries"}, {"backtrace": 7, "fragment": "-l<PERSON><PERSON><PERSON>", "role": "libraries"}, {"backtrace": 7, "fragment": "../../../components/packet/libpacket.a", "role": "libraries"}, {"backtrace": 7, "fragment": "../../singleton/libsingleton.a", "role": "libraries"}, {"backtrace": 7, "fragment": "../../environment/libenvironment.a", "role": "libraries"}, {"backtrace": 7, "fragment": "../../metric/libmetric.a", "role": "libraries"}, {"backtrace": 7, "fragment": "../../event_is/libevent_is.a", "role": "libraries"}, {"backtrace": 7, "fragment": "../../buffers/libbuffers.a", "role": "libraries"}, {"backtrace": 7, "fragment": "../../rest/librest.a", "role": "libraries"}, {"backtrace": 7, "fragment": "../../config/libconfig.a", "role": "libraries"}, {"backtrace": 7, "fragment": "../../compression/libcompression_utils.so", "role": "libraries"}, {"backtrace": 7, "fragment": "-lz", "role": "libraries"}, {"backtrace": 7, "fragment": "/usr/lib/x86_64-linux-gnu/libgtest.a", "role": "libraries"}, {"backtrace": 7, "fragment": "/usr/lib/x86_64-linux-gnu/libgtest_main.a", "role": "libraries"}, {"backtrace": 7, "fragment": "-lgmock", "role": "libraries"}, {"backtrace": 7, "fragment": "-lboost_regex", "role": "libraries"}, {"backtrace": 7, "fragment": "-l<PERSON><PERSON><PERSON>", "role": "libraries"}, {"backtrace": 7, "fragment": "-ldl", "role": "libraries"}, {"backtrace": 7, "fragment": "-Wl,--end-group", "role": "libraries"}, {"backtrace": 8, "fragment": "-lrt", "role": "libraries"}, {"backtrace": 7, "fragment": "-lz", "role": "libraries"}, {"backtrace": 7, "fragment": "-lgmock", "role": "libraries"}, {"backtrace": 7, "fragment": "-lboost_regex", "role": "libraries"}, {"backtrace": 7, "fragment": "-ldl", "role": "libraries"}, {"backtrace": 10, "fragment": "../../agent_core_utilities/libagent_core_utilities.a", "role": "libraries"}, {"backtrace": 7, "fragment": "/usr/lib/x86_64-linux-gnu/libgtest.a", "role": "libraries"}], "language": "CXX"}, "name": "shm_pkt_queue_ut", "nameOnDisk": "shm_pkt_queue_ut", "paths": {"build": "core/shm_pkt_queue/shm_pkt_queue_ut", "source": "core/shm_pkt_queue/shm_pkt_queue_ut"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 2, "compileGroupIndex": 0, "path": "core/shm_pkt_queue/shm_pkt_queue_ut/shm_pkt_queue_ut.cc", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}