{"archive": {}, "artifacts": [{"path": "core/messaging/libmessaging.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "include_directories"], "files": ["core/messaging/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 6, "parent": 0}, {"command": 1, "file": 0, "line": 7, "parent": 0}, {"file": 1}, {"command": 2, "file": 1, "line": 21, "parent": 3}, {"command": 2, "file": 1, "line": 22, "parent": 3}, {"command": 2, "file": 1, "line": 26, "parent": 3}, {"command": 2, "file": 1, "line": 27, "parent": 3}, {"command": 2, "file": 1, "line": 28, "parent": 3}, {"command": 2, "file": 1, "line": 29, "parent": 3}, {"command": 2, "file": 1, "line": 30, "parent": 3}, {"command": 2, "file": 1, "line": 31, "parent": 3}, {"command": 2, "file": 1, "line": 32, "parent": 3}, {"command": 2, "file": 1, "line": 33, "parent": 3}, {"command": 2, "file": 1, "line": 34, "parent": 3}, {"command": 2, "file": 1, "line": 35, "parent": 3}, {"command": 2, "file": 1, "line": 36, "parent": 3}, {"command": 2, "file": 1, "line": 37, "parent": 3}, {"command": 2, "file": 0, "line": 1, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -O2 -fPIC -Wall -Wno-terminate -g"}], "includes": [{"backtrace": 4, "path": "/usr/include/libxml2"}, {"backtrace": 5, "path": "/usr/src/googletest/googlemock/include"}, {"backtrace": 6, "path": "/home/<USER>/Code/openappsec/external"}, {"backtrace": 7, "path": "/home/<USER>/Code/openappsec/external/yajl/yajl-2.1.1/include"}, {"backtrace": 8, "path": "/home/<USER>/Code/openappsec/external/C-Mock/include/cmock"}, {"backtrace": 9, "path": "/home/<USER>/Code/openappsec/external/picojson"}, {"backtrace": 10, "path": "/home/<USER>/Code/openappsec/core/include/general"}, {"backtrace": 11, "path": "/home/<USER>/Code/openappsec/core/include/internal"}, {"backtrace": 12, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/interfaces"}, {"backtrace": 13, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/resources"}, {"backtrace": 14, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/utilities"}, {"backtrace": 15, "path": "/home/<USER>/Code/openappsec/core/include/attachments"}, {"backtrace": 16, "path": "/home/<USER>/Code/openappsec/events/include"}, {"backtrace": 17, "path": "/home/<USER>/Code/openappsec/components/include"}, {"backtrace": 18, "path": "/home/<USER>/Code/openappsec/core/messaging/include"}], "language": "CXX", "sourceIndexes": [0]}], "dependencies": [{"backtrace": 2, "id": "messaging_comp::@8afdec6b84cf90c070f7"}, {"backtrace": 2, "id": "connection::@0c7231ed830aec82add9"}, {"backtrace": 2, "id": "messaging_buffer_comp::@e95b015eba07c5bbf346"}], "id": "messaging::@ff33175e46fc4a6269a1", "name": "messaging", "nameOnDisk": "libmessaging.a", "paths": {"build": "core/messaging", "source": "core/messaging"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "core/messaging/messaging.cc", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}