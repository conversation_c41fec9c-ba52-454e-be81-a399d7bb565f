{"backtraceGraph": {"commands": ["install"], "files": ["nodes/http_transaction_handler/package/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 1, "parent": 0}, {"command": 0, "file": 0, "line": 2, "parent": 0}, {"command": 0, "file": 0, "line": 3, "parent": 0}, {"command": 0, "file": 0, "line": 4, "parent": 0}, {"command": 0, "file": 0, "line": 5, "parent": 0}, {"command": 0, "file": 0, "line": 6, "parent": 0}, {"command": 0, "file": 0, "line": 8, "parent": 0}, {"command": 0, "file": 0, "line": 9, "parent": 0}, {"command": 0, "file": 0, "line": 10, "parent": 0}]}, "installers": [{"backtrace": 1, "component": "Unspecified", "destination": "http_transaction_handler_service", "paths": ["nodes/http_transaction_handler/package/install-http-transaction-handler.sh"], "type": "file"}, {"backtrace": 2, "component": "Unspecified", "destination": "http_transaction_handler_service/conf", "paths": ["nodes/http_transaction_handler/package/cp-nano-http-transaction-handler-conf.json"], "type": "file"}, {"backtrace": 3, "component": "Unspecified", "destination": "http_transaction_handler_service/conf", "paths": ["nodes/http_transaction_handler/package/cp-nano-http-transaction-handler-conf-container.json"], "type": "file"}, {"backtrace": 4, "component": "Unspecified", "destination": "http_transaction_handler_service/conf", "paths": ["nodes/http_transaction_handler/package/cp-nano-http-transaction-handler-debug-conf.json"], "type": "file"}, {"backtrace": 5, "component": "Unspecified", "destination": "http_transaction_handler_service/conf", "paths": ["nodes/http_transaction_handler/package/cp-nano-http-transaction-handler.cfg"], "type": "file"}, {"backtrace": 6, "component": "Unspecified", "destination": "http_transaction_handler_service/bin", "paths": ["nodes/http_transaction_handler/package/k8s-log-file-handler.sh"], "type": "file"}, {"backtrace": 7, "component": "Unspecified", "destination": "http_transaction_handler_service/scripts/snort3_to_ips", "paths": [{"from": "nodes/http_transaction_handler/package/snort3_to_ips", "to": "."}], "type": "directory"}, {"backtrace": 8, "component": "Unspecified", "destination": "http_transaction_handler_service/scripts", "paths": ["nodes/http_transaction_handler/package/snort_to_ips_local.py"], "type": "file"}, {"backtrace": 9, "component": "Unspecified", "destination": "http_transaction_handler_service/scripts", "paths": ["nodes/http_transaction_handler/package/exception.py"], "type": "file"}], "paths": {"build": "nodes/http_transaction_handler/package", "source": "nodes/http_transaction_handler/package"}}