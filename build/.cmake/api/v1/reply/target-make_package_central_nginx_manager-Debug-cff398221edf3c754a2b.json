{"backtrace": 2, "backtraceGraph": {"commands": ["add_custom_target", "gen_package"], "files": ["nodes/packaging.cmake", "nodes/central_nginx_manager/CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 29, "parent": 0}, {"command": 0, "file": 0, "line": 45, "parent": 1}]}, "id": "make_package_central_nginx_manager::@2138b9ab414ffd11efe3", "name": "make_package_central_nginx_manager", "paths": {"build": "nodes/central_nginx_manager", "source": "nodes/central_nginx_manager"}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1, 2]}], "sources": [{"backtrace": 2, "isGenerated": true, "path": "build/nodes/central_nginx_manager/CMakeFiles/make_package_central_nginx_manager", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/nodes/central_nginx_manager/CMakeFiles/make_package_central_nginx_manager.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "/usr/local/install-cp-nano-central-nginx-manager.sh.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}