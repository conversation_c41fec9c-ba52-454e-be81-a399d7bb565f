{"inputs": [{"path": "CMakeLists.txt"}, {"isGenerated": true, "path": "build/CMakeFiles/3.28.3/CMakeSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Platform/Linux-Initialize.cmake"}, {"isGenerated": true, "path": "build/CMakeFiles/3.28.3/CMakeCCompiler.cmake"}, {"isGenerated": true, "path": "build/CMakeFiles/3.28.3/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Platform/Linux.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Platform/UnixPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/Clang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Platform/Linux-Clang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Platform/Linux-GNU-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Platform/Linux-GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/Clang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Platform/Linux-Clang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Platform/Linux-GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Platform/Linux-GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindBoost.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "/usr/lib/x86_64-linux-gnu/cmake/Boost-1.83.0/BoostConfigVersion.cmake"}, {"isExternal": true, "path": "/usr/lib/x86_64-linux-gnu/cmake/Boost-1.83.0/BoostConfig.cmake"}, {"isExternal": true, "path": "/usr/lib/x86_64-linux-gnu/cmake/boost_headers-1.83.0/boost_headers-config-version.cmake"}, {"isExternal": true, "path": "/usr/lib/x86_64-linux-gnu/cmake/boost_headers-1.83.0/boost_headers-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindZLIB.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/SelectLibraryConfigurations.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindGTest.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/GoogleTest.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "/usr/lib/x86_64-linux-gnu/cmake/GTest/GTestConfigVersion.cmake"}, {"isExternal": true, "path": "/usr/lib/x86_64-linux-gnu/cmake/GTest/GTestConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeFindDependencyMacro.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindThreads.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CheckLibraryExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CheckIncludeFile.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CheckCSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Internal/CheckSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "/usr/lib/x86_64-linux-gnu/cmake/GTest/GTestTargets.cmake"}, {"isExternal": true, "path": "/usr/lib/x86_64-linux-gnu/cmake/GTest/GTestTargets-none.cmake"}, {"isExternal": true, "path": "/usr/lib/x86_64-linux-gnu/cmake/GTest/GMockTargets.cmake"}, {"isExternal": true, "path": "/usr/lib/x86_64-linux-gnu/cmake/GTest/GMockTargets-none.cmake"}, {"path": "cppcheck.cmake"}, {"path": "unit_test.cmake"}, {"path": "build_system/CMakeLists.txt"}, {"path": "build_system/docker/CMakeLists.txt"}, {"path": "build_system/charts/CMakeLists.txt"}, {"path": "external/CMakeLists.txt"}, {"path": "external/graphqlparser/CMakeLists.txt"}, {"path": "external/graphqlparser/cmake/version.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindGit.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindPythonInterp.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindBISON.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindFLEX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindPkgConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindPackageMessage.cmake"}, {"path": "external/graphqlparser/libgraphqlparser.pc.in"}, {"path": "external/graphqlparser/python/CMakeLists.txt"}, {"path": "external/yajl/CMakeLists.txt"}, {"path": "external/yajl/YAJLDoc.cmake"}, {"path": "external/yajl/src/YAJL.dxy"}, {"path": "external/yajl/src/CMakeLists.txt"}, {"path": "external/yajl/src/api/yajl_version.h.cmake"}, {"path": "external/yajl/src/yajl.pc.cmake"}, {"path": "core/CMakeLists.txt"}, {"path": "core/cptest/CMakeLists.txt"}, {"path": "core/cptest/cptest_ut/CMakeLists.txt"}, {"path": "core/agent_core_utilities/CMakeLists.txt"}, {"path": "core/agent_core_utilities/agent_core_utilities_ut/CMakeLists.txt"}, {"path": "core/shell_cmd/CMakeLists.txt"}, {"path": "core/debug_is/CMakeLists.txt"}, {"path": "core/debug_is/debug_is_ut/CMakeLists.txt"}, {"path": "core/time_proxy/CMakeLists.txt"}, {"path": "core/time_proxy/time_proxy_ut/CMakeLists.txt"}, {"path": "core/singleton/CMakeLists.txt"}, {"path": "core/singleton/singleton_ut/CMakeLists.txt"}, {"path": "core/buffers/CMakeLists.txt"}, {"path": "core/buffers/buffers_ut/CMakeLists.txt"}, {"path": "core/mainloop/CMakeLists.txt"}, {"path": "core/mainloop/mainloop_ut/CMakeLists.txt"}, {"path": "core/environment/CMakeLists.txt"}, {"path": "core/environment/environment_ut/CMakeLists.txt"}, {"path": "core/table/CMakeLists.txt"}, {"path": "core/rest/CMakeLists.txt"}, {"path": "core/rest/rest_ut/CMakeLists.txt"}, {"path": "core/report/CMakeLists.txt"}, {"path": "core/report/report_ut/CMakeLists.txt"}, {"path": "core/logging/CMakeLists.txt"}, {"path": "core/logging/logging_ut/CMakeLists.txt"}, {"path": "core/connkey/CMakeLists.txt"}, {"path": "core/messaging/CMakeLists.txt"}, {"path": "core/messaging/messaging_comp/CMakeLists.txt"}, {"path": "core/messaging/messaging_comp/messaging_comp_ut/CMakeLists.txt"}, {"path": "core/messaging/connection/CMakeLists.txt"}, {"path": "core/messaging/connection/connection_ut/CMakeLists.txt"}, {"path": "core/messaging/messaging_buffer_comp/CMakeLists.txt"}, {"path": "core/messaging/messaging_buffer_comp/messaging_buffer_comp_ut/CMakeLists.txt"}, {"path": "core/messaging/messaging_buffer_comp/messaging_buffer_comp_ut/test_data/CMakeLists.txt"}, {"path": "core/config/CMakeLists.txt"}, {"path": "core/agent_details/CMakeLists.txt"}, {"path": "core/agent_details/agent_details_ut/CMakeLists.txt"}, {"path": "core/event_is/CMakeLists.txt"}, {"path": "core/event_is/event_ut/CMakeLists.txt"}, {"path": "core/encryptor/CMakeLists.txt"}, {"path": "core/encryptor/cpnano_base64/CMakeLists.txt"}, {"path": "core/encryptor/encryptor_ut/CMakeLists.txt"}, {"path": "core/intelligence_is_v2/CMakeLists.txt"}, {"path": "core/cpu/CMakeLists.txt"}, {"path": "core/cpu/cpu_ut/CMakeLists.txt"}, {"path": "core/memory_consumption/CMakeLists.txt"}, {"path": "core/memory_consumption/memory_consumption_ut/CMakeLists.txt"}, {"path": "core/shmem_ipc/CMakeLists.txt"}, {"path": "core/shmem_ipc/shmem_ipc_ut/CMakeLists.txt"}, {"path": "core/shm_pkt_queue/CMakeLists.txt"}, {"path": "core/shm_pkt_queue/shm_pkt_queue_ut/CMakeLists.txt"}, {"path": "core/instance_awareness/CMakeLists.txt"}, {"path": "core/instance_awareness/instance_awareness_ut/CMakeLists.txt"}, {"path": "core/socket_is/CMakeLists.txt"}, {"path": "core/agent_details_reporter/CMakeLists.txt"}, {"path": "core/agent_details_reporter/agent_details_reporter_ut/CMakeLists.txt"}, {"path": "core/metric/CMakeLists.txt"}, {"path": "core/metric/metric_ut/CMakeLists.txt"}, {"path": "core/version/CMakeLists.txt"}, {"path": "core/version/version_ut/CMakeLists.txt"}, {"path": "core/tenant_manager/CMakeLists.txt"}, {"path": "core/compression/CMakeLists.txt"}, {"path": "core/compression/compression_utils_ut/CMakeLists.txt"}, {"path": "core/attachments/CMakeLists.txt"}, {"path": "core/attachments/http_configuration/CMakeLists.txt"}, {"path": "core/attachments/http_configuration/http_configuration_ut/CMakeLists.txt"}, {"path": "core/report_messaging/CMakeLists.txt"}, {"path": "core/report_messaging/report_messaging_ut/CMakeLists.txt"}, {"path": "core/env_details/CMakeLists.txt"}, {"path": "core/core_ut/CMakeLists.txt"}, {"path": "attachments/CMakeLists.txt"}, {"path": "attachments/nginx/CMakeLists.txt"}, {"path": "attachments/nginx/nginx_attachment_util/CMakeLists.txt"}, {"path": "attachments/nginx/nginx_attachment_util/nginx_attachment_util_ut/CMakeLists.txt"}, {"path": "components/CMakeLists.txt"}, {"path": "components/http_manager/CMakeLists.txt"}, {"path": "components/signal_handler/CMakeLists.txt"}, {"path": "components/gradual_deployment/CMakeLists.txt"}, {"path": "components/gradual_deployment/gradual_deployment_ut/CMakeLists.txt"}, {"path": "components/packet/CMakeLists.txt"}, {"path": "components/packet/packet_ut/CMakeLists.txt"}, {"path": "components/pending_key/CMakeLists.txt"}, {"path": "components/pending_key/pending_key_ut/CMakeLists.txt"}, {"path": "components/utils/CMakeLists.txt"}, {"path": "components/utils/generic_rulebase/CMakeLists.txt"}, {"path": "components/utils/generic_rulebase/evaluators/CMakeLists.txt"}, {"path": "components/utils/geo_location/CMakeLists.txt"}, {"path": "components/utils/http_transaction_data/CMakeLists.txt"}, {"path": "components/utils/http_transaction_data/http_transaction_data_ut/CMakeLists.txt"}, {"path": "components/utils/ip_utilities/CMakeLists.txt"}, {"path": "components/utils/keywords/CMakeLists.txt"}, {"path": "components/utils/keywords/keywords_ut/CMakeLists.txt"}, {"path": "components/utils/pm/CMakeLists.txt"}, {"path": "components/utils/pm/pm_ut/CMakeLists.txt"}, {"path": "components/utils/service_health_status/CMakeLists.txt"}, {"path": "components/utils/service_health_status/service_health_status_ut/CMakeLists.txt"}, {"path": "components/utils/nginx_utils/CMakeLists.txt"}, {"path": "components/utils/utilities/CMakeLists.txt"}, {"path": "components/utils/utilities/nginx_conf_collector/CMakeLists.txt"}, {"path": "components/attachment-intakers/CMakeLists.txt"}, {"path": "components/attachment-intakers/nginx_attachment/CMakeLists.txt"}, {"path": "components/attachment-intakers/attachment_registrator/CMakeLists.txt"}, {"path": "components/security_apps/CMakeLists.txt"}, {"path": "components/security_apps/http_geo_filter/CMakeLists.txt"}, {"path": "components/security_apps/ips/CMakeLists.txt"}, {"path": "components/security_apps/ips/ips_ut/CMakeLists.txt"}, {"path": "components/security_apps/layer_7_access_control/CMakeLists.txt"}, {"path": "components/security_apps/layer_7_access_control/layer_7_access_control_ut/CMakeLists.txt"}, {"path": "components/security_apps/local_policy_mgmt_gen/CMakeLists.txt"}, {"path": "components/security_apps/orchestration/CMakeLists.txt"}, {"path": "components/security_apps/orchestration/orchestration_tools/CMakeLists.txt"}, {"path": "components/security_apps/orchestration/modules/CMakeLists.txt"}, {"path": "components/security_apps/orchestration/downloader/CMakeLists.txt"}, {"path": "components/security_apps/orchestration/service_controller/CMakeLists.txt"}, {"path": "components/security_apps/orchestration/package_handler/CMakeLists.txt"}, {"path": "components/security_apps/orchestration/manifest_controller/CMakeLists.txt"}, {"path": "components/security_apps/orchestration/update_communication/CMakeLists.txt"}, {"path": "components/security_apps/orchestration/details_resolver/CMakeLists.txt"}, {"path": "components/security_apps/orchestration/health_check/CMakeLists.txt"}, {"path": "components/security_apps/orchestration/health_check/health_check_ut/CMakeLists.txt"}, {"path": "components/security_apps/orchestration/health_check_manager/CMakeLists.txt"}, {"path": "components/security_apps/orchestration/updates_process_reporter/CMakeLists.txt"}, {"path": "components/security_apps/orchestration/external_sdk_server/CMakeLists.txt"}, {"path": "components/security_apps/orchestration/external_sdk_server/external_sdk_server_ut/CMakeLists.txt"}, {"path": "components/security_apps/prometheus/CMakeLists.txt"}, {"path": "components/security_apps/prometheus/prometheus_ut/CMakeLists.txt"}, {"path": "components/security_apps/rate_limit/CMakeLists.txt"}, {"path": "components/security_apps/waap/CMakeLists.txt"}, {"path": "components/security_apps/waap/waap_clib/CMakeLists.txt"}, {"path": "components/security_apps/waap/reputation/CMakeLists.txt"}, {"path": "components/security_apps/central_nginx_manager/CMakeLists.txt"}, {"path": "components/nginx_message_reader/CMakeLists.txt"}, {"path": "nodes/CMakeLists.txt"}, {"path": "nodes/packaging.cmake"}, {"path": "nodes/orchestration/CMakeLists.txt"}, {"path": "nodes/orchestration/package/CMakeLists.txt"}, {"path": "nodes/orchestration/package/cpnano_debug/CMakeLists.txt"}, {"path": "nodes/orchestration/package/cpnano_json/CMakeLists.txt"}, {"path": "nodes/prometheus/CMakeLists.txt"}, {"path": "nodes/prometheus/package/CMakeLists.txt"}, {"path": "nodes/agent_cache/CMakeLists.txt"}, {"path": "nodes/agent_cache/package/CMakeLists.txt"}, {"path": "nodes/http_transaction_handler/CMakeLists.txt"}, {"path": "nodes/http_transaction_handler/package/CMakeLists.txt"}, {"path": "nodes/attachment_registration_manager/CMakeLists.txt"}, {"path": "nodes/attachment_registration_manager/package/CMakeLists.txt"}, {"path": "nodes/central_nginx_manager/CMakeLists.txt"}, {"path": "nodes/central_nginx_manager/package/CMakeLists.txt"}], "kind": "cmakeFiles", "paths": {"build": "/home/<USER>/Code/openappsec/build", "source": "/home/<USER>/Code/openappsec"}, "version": {"major": 1, "minor": 0}}