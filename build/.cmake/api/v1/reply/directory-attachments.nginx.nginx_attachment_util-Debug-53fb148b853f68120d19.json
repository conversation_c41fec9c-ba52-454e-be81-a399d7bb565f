{"backtraceGraph": {"commands": ["install"], "files": ["attachments/nginx/nginx_attachment_util/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 7, "parent": 0}, {"command": 0, "file": 0, "line": 8, "parent": 0}]}, "installers": [{"backtrace": 1, "component": "Unspecified", "destination": "lib", "paths": ["attachments/nginx/nginx_attachment_util/libnginx_attachment_util.so"], "targetId": "nginx_attachment_util::@b089a24a02c75410debb", "targetIndex": 91, "type": "target"}, {"backtrace": 1, "component": "Unspecified", "cxxModuleBmiTarget": {"id": "nginx_attachment_util::@b089a24a02c75410debb", "index": 91}, "destination": "lib", "type": "cxxModuleBmi"}, {"backtrace": 2, "component": "Unspecified", "destination": "http_transaction_handler_service/lib", "paths": ["attachments/nginx/nginx_attachment_util/libnginx_attachment_util.so"], "targetId": "nginx_attachment_util::@b089a24a02c75410debb", "targetIndex": 91, "type": "target"}, {"backtrace": 2, "component": "Unspecified", "cxxModuleBmiTarget": {"id": "nginx_attachment_util::@b089a24a02c75410debb", "index": 91}, "destination": "http_transaction_handler_service/lib", "type": "cxxModuleBmi"}], "paths": {"build": "attachments/nginx/nginx_attachment_util", "source": "attachments/nginx/nginx_attachment_util"}}