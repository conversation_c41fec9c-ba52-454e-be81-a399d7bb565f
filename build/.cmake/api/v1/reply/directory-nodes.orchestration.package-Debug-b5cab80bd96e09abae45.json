{"backtraceGraph": {"commands": ["install"], "files": ["nodes/orchestration/package/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 4, "parent": 0}, {"command": 0, "file": 0, "line": 5, "parent": 0}, {"command": 0, "file": 0, "line": 7, "parent": 0}, {"command": 0, "file": 0, "line": 8, "parent": 0}, {"command": 0, "file": 0, "line": 9, "parent": 0}, {"command": 0, "file": 0, "line": 10, "parent": 0}, {"command": 0, "file": 0, "line": 11, "parent": 0}, {"command": 0, "file": 0, "line": 13, "parent": 0}, {"command": 0, "file": 0, "line": 14, "parent": 0}, {"command": 0, "file": 0, "line": 15, "parent": 0}, {"command": 0, "file": 0, "line": 16, "parent": 0}, {"command": 0, "file": 0, "line": 18, "parent": 0}, {"command": 0, "file": 0, "line": 19, "parent": 0}, {"command": 0, "file": 0, "line": 20, "parent": 0}, {"command": 0, "file": 0, "line": 21, "parent": 0}, {"command": 0, "file": 0, "line": 22, "parent": 0}, {"command": 0, "file": 0, "line": 23, "parent": 0}, {"command": 0, "file": 0, "line": 24, "parent": 0}, {"command": 0, "file": 0, "line": 25, "parent": 0}, {"command": 0, "file": 0, "line": 26, "parent": 0}, {"command": 0, "file": 0, "line": 27, "parent": 0}, {"command": 0, "file": 0, "line": 28, "parent": 0}, {"command": 0, "file": 0, "line": 29, "parent": 0}, {"command": 0, "file": 0, "line": 31, "parent": 0}, {"command": 0, "file": 0, "line": 32, "parent": 0}, {"command": 0, "file": 0, "line": 33, "parent": 0}]}, "installers": [{"backtrace": 1, "component": "Unspecified", "destination": "./orchestration/EULA.txt", "paths": ["nodes/orchestration/package/EULA.txt"], "type": "file"}, {"backtrace": 2, "component": "Unspecified", "destination": "./orchestration/Licenses-for-Third-Party-Components.txt", "paths": ["nodes/orchestration/package/Licenses-for-Third-Party-Components.txt"], "type": "file"}, {"backtrace": 3, "component": "Unspecified", "destination": "./orchestration", "paths": ["nodes/orchestration/package/orchestration_package.sh"], "type": "file"}, {"backtrace": 4, "component": "Unspecified", "destination": "./orchestration", "paths": ["nodes/orchestration/package/cp-agent-info.sh"], "type": "file"}, {"backtrace": 5, "component": "Unspecified", "destination": "./orchestration", "paths": ["nodes/orchestration/package/k8s-check-update-listener.sh"], "type": "file"}, {"backtrace": 6, "component": "Unspecified", "destination": "./orchestration", "paths": ["nodes/orchestration/package/k8s-check-update-trigger.sh"], "type": "file"}, {"backtrace": 7, "component": "Unspecified", "destination": "./orchestration/scripts", "paths": ["nodes/orchestration/package/get-cloud-metadata.sh"], "type": "file"}, {"backtrace": 8, "component": "Unspecified", "destination": "./orchestration", "paths": ["nodes/orchestration/package/cp-agent-uninstall.sh"], "type": "file"}, {"backtrace": 9, "component": "Unspecified", "destination": "./orchestration", "paths": ["nodes/orchestration/package/cp-nano-cli.sh"], "type": "file"}, {"backtrace": 10, "component": "Unspecified", "destination": "./orchestration", "paths": ["nodes/orchestration/package/open-appsec-ctl.sh"], "type": "file"}, {"backtrace": 11, "component": "Unspecified", "destination": "./orchestration", "paths": ["nodes/orchestration/package/cp-nano-package-list"], "type": "file"}, {"backtrace": 12, "component": "Unspecified", "destination": "./orchestration/service/arm32_openwrt", "paths": ["nodes/orchestration/package/service/arm32_openwrt/nano_agent.init"], "type": "file"}, {"backtrace": 13, "component": "Unspecified", "destination": "./orchestration/service/smb", "paths": ["nodes/orchestration/package/service/smb/nano_agent.init"], "type": "file"}, {"backtrace": 14, "component": "Unspecified", "destination": "./orchestration/service/x86/ubuntu16", "paths": ["nodes/orchestration/package/service/x86/ubuntu16/nano_agent.service"], "type": "file"}, {"backtrace": 15, "component": "Unspecified", "destination": "./orchestration/service/x86/ubuntu14", "paths": ["nodes/orchestration/package/service/x86/ubuntu14/nano_agent.conf"], "type": "file"}, {"backtrace": 16, "component": "Unspecified", "destination": "./orchestration/service/x86/ubuntu14", "paths": ["nodes/orchestration/package/service/x86/ubuntu14/nano_agent.init"], "type": "file"}, {"backtrace": 17, "component": "Unspecified", "destination": "./orchestration/configuration", "paths": ["nodes/orchestration/package/configuration/orchestration.cfg"], "type": "file"}, {"backtrace": 18, "component": "Unspecified", "destination": "./orchestration/configuration", "paths": ["nodes/orchestration/package/configuration/cp-nano-orchestration-conf.json"], "type": "file"}, {"backtrace": 19, "component": "Unspecified", "destination": "./orchestration/configuration", "paths": ["nodes/orchestration/package/configuration/cp-nano-orchestration-debug-conf.json"], "type": "file"}, {"backtrace": 20, "component": "Unspecified", "destination": "./orchestration/watchdog", "paths": ["nodes/orchestration/package/watchdog/watchdog"], "type": "file"}, {"backtrace": 21, "component": "Unspecified", "destination": "./orchestration/watchdog", "paths": ["nodes/orchestration/package/watchdog/wait-for-networking-inspection-modules.sh"], "type": "file"}, {"backtrace": 22, "component": "Unspecified", "destination": "./orchestration/watchdog", "paths": ["nodes/orchestration/package/watchdog/access_pre_init"], "type": "file"}, {"backtrace": 23, "component": "Unspecified", "destination": "./orchestration/watchdog", "paths": ["nodes/orchestration/package/watchdog/revert_orchestrator_version.sh"], "type": "file"}, {"backtrace": 24, "component": "Unspecified", "destination": "./orchestration", "paths": ["nodes/orchestration/package/local-default-policy.yaml"], "type": "file"}, {"backtrace": 25, "component": "Unspecified", "destination": "./orchestration", "paths": ["nodes/orchestration/package/open-appsec-cloud-mgmt"], "type": "file"}, {"backtrace": 26, "component": "Unspecified", "destination": "./orchestration", "paths": ["nodes/orchestration/package/open-appsec-cloud-mgmt-k8s"], "type": "file"}], "paths": {"build": "nodes/orchestration/package", "source": "nodes/orchestration/package"}}