{"backtraceGraph": {"commands": ["install"], "files": ["nodes/orchestration/package/cpnano_json/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 3, "parent": 0}, {"command": 0, "file": 0, "line": 4, "parent": 0}]}, "installers": [{"backtrace": 1, "component": "Unspecified", "destination": "bin", "paths": ["nodes/orchestration/package/cpnano_json/cpnano_json"], "targetId": "cpnano_json::@7c7874a7130ae5225a4d", "targetIndex": 23, "type": "target"}, {"backtrace": 1, "component": "Unspecified", "cxxModuleBmiTarget": {"id": "cpnano_json::@7c7874a7130ae5225a4d", "index": 23}, "destination": "bin", "type": "cxxModuleBmi"}, {"backtrace": 2, "component": "Unspecified", "destination": "orchestration", "paths": ["nodes/orchestration/package/cpnano_json/cpnano_json"], "targetId": "cpnano_json::@7c7874a7130ae5225a4d", "targetIndex": 23, "type": "target"}, {"backtrace": 2, "component": "Unspecified", "cxxModuleBmiTarget": {"id": "cpnano_json::@7c7874a7130ae5225a4d", "index": 23}, "destination": "orchestration", "type": "cxxModuleBmi"}], "paths": {"build": "nodes/orchestration/package/cpnano_json", "source": "nodes/orchestration/package/cpnano_json"}}