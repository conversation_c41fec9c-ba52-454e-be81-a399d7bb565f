{"archive": {}, "artifacts": [{"path": "components/security_apps/waap/waap_clib/libwaap_clib.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "include_directories"], "files": ["components/security_apps/waap/waap_clib/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 2, "parent": 0}, {"file": 1}, {"command": 1, "file": 1, "line": 21, "parent": 2}, {"command": 1, "file": 1, "line": 22, "parent": 2}, {"command": 1, "file": 1, "line": 26, "parent": 2}, {"command": 1, "file": 1, "line": 27, "parent": 2}, {"command": 1, "file": 1, "line": 28, "parent": 2}, {"command": 1, "file": 1, "line": 29, "parent": 2}, {"command": 1, "file": 1, "line": 30, "parent": 2}, {"command": 1, "file": 1, "line": 31, "parent": 2}, {"command": 1, "file": 1, "line": 32, "parent": 2}, {"command": 1, "file": 1, "line": 33, "parent": 2}, {"command": 1, "file": 1, "line": 34, "parent": 2}, {"command": 1, "file": 1, "line": 35, "parent": 2}, {"command": 1, "file": 1, "line": 36, "parent": 2}, {"command": 1, "file": 1, "line": 37, "parent": 2}, {"command": 1, "file": 0, "line": 1, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -O2 -fPIC -Wall -Wno-terminate -g   -Wno-unused-function -Wno-unused-parameter -Wno-deprecated-declarations"}], "includes": [{"backtrace": 3, "path": "/usr/include/libxml2"}, {"backtrace": 4, "path": "/usr/src/googletest/googlemock/include"}, {"backtrace": 5, "path": "/home/<USER>/Code/openappsec/external"}, {"backtrace": 6, "path": "/home/<USER>/Code/openappsec/external/yajl/yajl-2.1.1/include"}, {"backtrace": 7, "path": "/home/<USER>/Code/openappsec/external/C-Mock/include/cmock"}, {"backtrace": 8, "path": "/home/<USER>/Code/openappsec/external/picojson"}, {"backtrace": 9, "path": "/home/<USER>/Code/openappsec/core/include/general"}, {"backtrace": 10, "path": "/home/<USER>/Code/openappsec/core/include/internal"}, {"backtrace": 11, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/interfaces"}, {"backtrace": 12, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/resources"}, {"backtrace": 13, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/utilities"}, {"backtrace": 14, "path": "/home/<USER>/Code/openappsec/core/include/attachments"}, {"backtrace": 15, "path": "/home/<USER>/Code/openappsec/events/include"}, {"backtrace": 16, "path": "/home/<USER>/Code/openappsec/components/include"}, {"backtrace": 17, "path": "/home/<USER>/Code/openappsec/components/security_apps/waap/waap_clib/../include"}], "language": "CXX", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91]}], "id": "waap_clib::@7c8cd5ce64b722c32cbf", "name": "waap_clib", "nameOnDisk": "libwaap_clib.a", "paths": {"build": "components/security_apps/waap/waap_clib", "source": "components/security_apps/waap/waap_clib"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/Csrf.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/CsrfPolicy.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/ContentTypeParser.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/CidrMatch.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/DeepParser.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/KeyStack.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/ParserBase.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/ParserBinary.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/ParserHdrValue.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/ParserJson.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/ParserMultipartForm.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/ParserRaw.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/ParserUrlEncode.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/ParserXML.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/ParserDelimiter.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/ParserConfluence.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/ParserHTML.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/PatternMatcher.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/PHPSerializedDataParser.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/WaapScores.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/WaapKeywords.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/Waf2Engine.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/Waf2EngineGetters.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/WaapScanner.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/WaapRegexPreconditions.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/Waf2Regex.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/WaapAssetState.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/Signatures.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/Waf2Util.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/WaapConfigBase.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/WaapConfigApi.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/WaapConfigApplication.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/BehaviorAnalysis.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/FpMitigation.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/D2Main.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/DeepAnalyzer.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/Telemetry.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/WaapOverride.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/ScoreBuilder.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/WaapTrigger.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/WaapDecision.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/WaapResponseInspectReasons.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/WaapResponseInjectReasons.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/WaapResultJson.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/WaapAssetStatesManager.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/Serializator.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/IndicatorsFilterBase.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/TypeIndicatorsFilter.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/KeywordIndicatorFilter.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/WaapOverrideFunctor.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/WaapValueStatsAnalyzer.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/TrustedSources.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/WaapParameters.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/IndicatorsFiltersManager.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/ConfidenceFile.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/ConfidenceCalculator.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/TrustedSourcesConfidence.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/RateLimiter.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/RateLimiting.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/ErrorLimiting.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/WaapErrorDisclosurePolicy.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/WaapOpenRedirect.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/WaapOpenRedirectPolicy.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/KeywordTypeValidator.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/SecurityHeadersPolicy.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/UserLimitsPolicy.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/ScannerDetector.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/TuningDecision.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/ScanResult.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/SingleDecision.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/DecisionFactory.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/AutonomousSecurityDecision.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/CsrfDecision.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/OpenRedirectDecision.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/ErrorDisclosureDecision.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/RateLimitingDecision.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/UserLimitsDecision.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/ErrorLimitingDecision.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/WaapConversions.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/SyncLearningNotification.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/LogGenWrapper.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/WaapSampleValue.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/ParserGql.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/ParserPercentEncode.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/ParserPairs.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/Waf2Util2.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/ParserPDF.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/ParserKnownBenignSkipper.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/ParserScreenedJson.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/ParserBinaryFile.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/RegexComparator.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/security_apps/waap/waap_clib/RequestsMonitor.cc", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}