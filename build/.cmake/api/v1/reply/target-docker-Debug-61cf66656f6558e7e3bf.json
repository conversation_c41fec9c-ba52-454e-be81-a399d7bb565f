{"backtrace": 1, "backtraceGraph": {"commands": ["add_custom_target"], "files": ["build_system/docker/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 9, "parent": 0}]}, "id": "docker::@693410d7d455bc60e04f", "name": "docker", "paths": {"build": "build_system/docker", "source": "build_system/docker"}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1, 2]}], "sources": [{"backtrace": 1, "isGenerated": true, "path": "build/build_system/docker/CMakeFiles/docker", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/build_system/docker/CMakeFiles/docker.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "/usr/local/agent-docker.img.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}