{"archive": {}, "artifacts": [{"path": "components/utils/keywords/libkeywords.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "include_directories"], "files": ["components/utils/keywords/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 1, "parent": 0}, {"file": 1}, {"command": 1, "file": 1, "line": 21, "parent": 2}, {"command": 1, "file": 1, "line": 22, "parent": 2}, {"command": 1, "file": 1, "line": 26, "parent": 2}, {"command": 1, "file": 1, "line": 27, "parent": 2}, {"command": 1, "file": 1, "line": 28, "parent": 2}, {"command": 1, "file": 1, "line": 29, "parent": 2}, {"command": 1, "file": 1, "line": 30, "parent": 2}, {"command": 1, "file": 1, "line": 31, "parent": 2}, {"command": 1, "file": 1, "line": 32, "parent": 2}, {"command": 1, "file": 1, "line": 33, "parent": 2}, {"command": 1, "file": 1, "line": 34, "parent": 2}, {"command": 1, "file": 1, "line": 35, "parent": 2}, {"command": 1, "file": 1, "line": 36, "parent": 2}, {"command": 1, "file": 1, "line": 37, "parent": 2}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -O2 -fPIC -Wall -Wno-terminate -g"}], "includes": [{"backtrace": 3, "path": "/usr/include/libxml2"}, {"backtrace": 4, "path": "/usr/src/googletest/googlemock/include"}, {"backtrace": 5, "path": "/home/<USER>/Code/openappsec/external"}, {"backtrace": 6, "path": "/home/<USER>/Code/openappsec/external/yajl/yajl-2.1.1/include"}, {"backtrace": 7, "path": "/home/<USER>/Code/openappsec/external/C-Mock/include/cmock"}, {"backtrace": 8, "path": "/home/<USER>/Code/openappsec/external/picojson"}, {"backtrace": 9, "path": "/home/<USER>/Code/openappsec/core/include/general"}, {"backtrace": 10, "path": "/home/<USER>/Code/openappsec/core/include/internal"}, {"backtrace": 11, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/interfaces"}, {"backtrace": 12, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/resources"}, {"backtrace": 13, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/utilities"}, {"backtrace": 14, "path": "/home/<USER>/Code/openappsec/core/include/attachments"}, {"backtrace": 15, "path": "/home/<USER>/Code/openappsec/events/include"}, {"backtrace": 16, "path": "/home/<USER>/Code/openappsec/components/include"}], "language": "CXX", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]}], "id": "keywords::@8a47f11b85238419b0e0", "name": "keywords", "nameOnDisk": "libkeywords.a", "paths": {"build": "components/utils/keywords", "source": "components/utils/keywords"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "components/utils/keywords/keywords_rule.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/utils/keywords/single_keyword.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/utils/keywords/data_keyword.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/utils/keywords/pcre_keyword.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/utils/keywords/length_keyword.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/utils/keywords/byte_extract_keyword.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/utils/keywords/compare_keyword.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/utils/keywords/jump_keyword.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/utils/keywords/stateop_keyword.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/utils/keywords/no_match_keyword.cc", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}