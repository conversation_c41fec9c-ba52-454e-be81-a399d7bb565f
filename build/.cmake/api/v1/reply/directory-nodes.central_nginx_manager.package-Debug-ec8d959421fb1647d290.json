{"backtraceGraph": {"commands": ["install"], "files": ["nodes/central_nginx_manager/package/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 1, "parent": 0}, {"command": 0, "file": 0, "line": 2, "parent": 0}, {"command": 0, "file": 0, "line": 3, "parent": 0}, {"command": 0, "file": 0, "line": 4, "parent": 0}]}, "installers": [{"backtrace": 1, "component": "Unspecified", "destination": "central_nginx_manager", "paths": ["nodes/central_nginx_manager/package/install-cp-nano-central-nginx-manager.sh"], "type": "file"}, {"backtrace": 2, "component": "Unspecified", "destination": "central_nginx_manager/conf", "paths": ["nodes/central_nginx_manager/package/cp-nano-central-nginx-manager.cfg"], "type": "file"}, {"backtrace": 3, "component": "Unspecified", "destination": "central_nginx_manager/conf", "paths": ["nodes/central_nginx_manager/package/cp-nano-central-nginx-manager-conf.json"], "type": "file"}, {"backtrace": 4, "component": "Unspecified", "destination": "central_nginx_manager/conf", "paths": ["nodes/central_nginx_manager/package/cp-nano-central-nginx-manager-debug-conf.json"], "type": "file"}], "paths": {"build": "nodes/central_nginx_manager/package", "source": "nodes/central_nginx_manager/package"}}