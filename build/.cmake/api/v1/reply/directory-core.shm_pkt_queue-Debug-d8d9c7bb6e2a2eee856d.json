{"backtraceGraph": {"commands": ["install"], "files": ["core/shm_pkt_queue/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 11, "parent": 0}]}, "installers": [{"backtrace": 1, "component": "Unspecified", "destination": "lib", "paths": ["core/shm_pkt_queue/libshm_pkt_queue.so"], "targetId": "shm_pkt_queue::@a805dd128f5d38de9bfb", "targetIndex": 127, "type": "target"}, {"backtrace": 1, "component": "Unspecified", "cxxModuleBmiTarget": {"id": "shm_pkt_queue::@a805dd128f5d38de9bfb", "index": 127}, "destination": "lib", "type": "cxxModuleBmi"}], "paths": {"build": "core/shm_pkt_queue", "source": "core/shm_pkt_queue"}}