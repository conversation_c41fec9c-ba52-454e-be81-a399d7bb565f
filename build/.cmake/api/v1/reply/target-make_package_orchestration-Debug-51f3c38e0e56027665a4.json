{"backtrace": 2, "backtraceGraph": {"commands": ["add_custom_target", "gen_package"], "files": ["nodes/packaging.cmake", "nodes/orchestration/CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 178, "parent": 0}, {"command": 0, "file": 0, "line": 45, "parent": 1}]}, "id": "make_package_orchestration::@b474fdd263f216d3bed2", "name": "make_package_orchestration", "paths": {"build": "nodes/orchestration", "source": "nodes/orchestration"}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1, 2]}], "sources": [{"backtrace": 2, "isGenerated": true, "path": "build/nodes/orchestration/CMakeFiles/make_package_orchestration", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/nodes/orchestration/CMakeFiles/make_package_orchestration.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "/usr/local/install-cp-nano-agent.sh.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}