{"backtrace": 1, "backtraceGraph": {"commands": ["add_custom_target"], "files": ["build_system/charts/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 6, "parent": 0}]}, "id": "charts::@c7c92f2da9c474e4cb9b", "name": "charts", "paths": {"build": "build_system/charts", "source": "build_system/charts"}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1, 2]}], "sources": [{"backtrace": 1, "isGenerated": true, "path": "build/build_system/charts/CMakeFiles/charts", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/build_system/charts/CMakeFiles/charts.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "/usr/local/open-appsec-k8s-nginx-ingress-4.1.4.tgz.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}