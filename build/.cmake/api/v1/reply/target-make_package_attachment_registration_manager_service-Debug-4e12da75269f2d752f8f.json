{"backtrace": 2, "backtraceGraph": {"commands": ["add_custom_target", "gen_package"], "files": ["nodes/packaging.cmake", "nodes/attachment_registration_manager/CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 25, "parent": 0}, {"command": 0, "file": 0, "line": 45, "parent": 1}]}, "id": "make_package_attachment_registration_manager_service::@5599c499f1a018a19a5d", "name": "make_package_attachment_registration_manager_service", "paths": {"build": "nodes/attachment_registration_manager", "source": "nodes/attachment_registration_manager"}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1, 2]}], "sources": [{"backtrace": 2, "isGenerated": true, "path": "build/nodes/attachment_registration_manager/CMakeFiles/make_package_attachment_registration_manager_service", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/nodes/attachment_registration_manager/CMakeFiles/make_package_attachment_registration_manager_service.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "/usr/local/install-cp-nano-attachment-registration-manager.sh.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}