{"artifacts": [{"path": "core/messaging/messaging_buffer_comp/messaging_buffer_comp_ut/messaging_buffer_comp_ut"}], "backtrace": 2, "backtraceGraph": {"commands": ["add_executable", "add_unit_test", "link_directories", "target_link_libraries", "include_directories"], "files": ["unit_test.cmake", "core/messaging/messaging_buffer_comp/messaging_buffer_comp_ut/CMakeLists.txt", "CMakeLists.txt", "core/config/CMakeLists.txt", "core/messaging/CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 1, "parent": 0}, {"command": 0, "file": 0, "line": 4, "parent": 1}, {"file": 2}, {"command": 2, "file": 2, "line": 20, "parent": 3}, {"command": 3, "file": 0, "line": 5, "parent": 1}, {"file": 3}, {"command": 3, "file": 3, "line": 2, "parent": 6}, {"command": 4, "file": 2, "line": 21, "parent": 3}, {"command": 4, "file": 2, "line": 22, "parent": 3}, {"command": 4, "file": 2, "line": 26, "parent": 3}, {"command": 4, "file": 2, "line": 27, "parent": 3}, {"command": 4, "file": 2, "line": 28, "parent": 3}, {"command": 4, "file": 2, "line": 29, "parent": 3}, {"command": 4, "file": 2, "line": 30, "parent": 3}, {"command": 4, "file": 2, "line": 31, "parent": 3}, {"command": 4, "file": 2, "line": 32, "parent": 3}, {"command": 4, "file": 2, "line": 33, "parent": 3}, {"command": 4, "file": 2, "line": 34, "parent": 3}, {"command": 4, "file": 2, "line": 35, "parent": 3}, {"command": 4, "file": 2, "line": 36, "parent": 3}, {"command": 4, "file": 2, "line": 37, "parent": 3}, {"file": 4}, {"command": 4, "file": 4, "line": 1, "parent": 22}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -O2 -fPIC -Wall -Wno-terminate -g"}, {"backtrace": 5, "fragment": "-DGTEST_HAS_PTHREAD=1"}], "includes": [{"backtrace": 8, "path": "/usr/include/libxml2"}, {"backtrace": 9, "path": "/usr/src/googletest/googlemock/include"}, {"backtrace": 10, "path": "/home/<USER>/Code/openappsec/external"}, {"backtrace": 11, "path": "/home/<USER>/Code/openappsec/external/yajl/yajl-2.1.1/include"}, {"backtrace": 12, "path": "/home/<USER>/Code/openappsec/external/C-Mock/include/cmock"}, {"backtrace": 13, "path": "/home/<USER>/Code/openappsec/external/picojson"}, {"backtrace": 14, "path": "/home/<USER>/Code/openappsec/core/include/general"}, {"backtrace": 15, "path": "/home/<USER>/Code/openappsec/core/include/internal"}, {"backtrace": 16, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/interfaces"}, {"backtrace": 17, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/resources"}, {"backtrace": 18, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/utilities"}, {"backtrace": 19, "path": "/home/<USER>/Code/openappsec/core/include/attachments"}, {"backtrace": 20, "path": "/home/<USER>/Code/openappsec/events/include"}, {"backtrace": 21, "path": "/home/<USER>/Code/openappsec/components/include"}, {"backtrace": 23, "path": "/home/<USER>/Code/openappsec/core/messaging/include"}], "language": "CXX", "sourceIndexes": [0]}], "dependencies": [{"backtrace": 5, "id": "cptest::@0c5334b3e4869b117d9f"}, {"backtrace": 5, "id": "agent_core_utilities::@a13315131675cfa87ba6"}, {"backtrace": 5, "id": "shell_cmd::@11ee51f991ec943c1614"}, {"backtrace": 5, "id": "debug_is::@a0c1859d2372bc787301"}, {"backtrace": 5, "id": "time_proxy::@b3dbbbe1cfe4be1867c6"}, {"backtrace": 5, "id": "singleton::@1e8aa1f95e3638a33048"}, {"backtrace": 5, "id": "buffers::@383b6442fe4f30043a47"}, {"backtrace": 5, "id": "environment::@42a05e874c90aabce0a2"}, {"backtrace": 5, "id": "rest::@552c489a81002f1765c1"}, {"backtrace": 5, "id": "report::@1270cc86e0390e7112d3"}, {"backtrace": 5, "id": "messaging_comp::@8afdec6b84cf90c070f7"}, {"backtrace": 5, "id": "messaging_buffer_comp::@e95b015eba07c5bbf346"}, {"backtrace": 5, "id": "config::@d0a6a499a33ca3bb4e51"}, {"backtrace": 5, "id": "agent_details::@f2f09c82ed669bc3ab7a"}, {"backtrace": 5, "id": "event_is::@262d1818dfec6bf12713"}, {"backtrace": 5, "id": "instance_awareness::@5f197407e6180805be5b"}, {"backtrace": 5, "id": "metric::@ead7061abeb3290a9386"}, {"backtrace": 5, "id": "version::@a1ad84cd100617c107a5"}, {"backtrace": 5, "id": "compression_utils::@3dcc85d11fba6ff84d38"}, {"backtrace": 5, "id": "packet::@db2bc6f12dc570b4bd47"}], "id": "messaging_buffer_comp_ut::@e0bae46f522f7cbc1156", "link": {"commandFragments": [{"fragment": "-O2 -fPIC -Wall -Wno-terminate -g", "role": "flags"}, {"fragment": "-rdynamic", "role": "flags"}, {"backtrace": 4, "fragment": "-L/usr/lib/x86_64-linux-gnu/libz.so", "role": "libraryPath"}, {"fragment": "-Wl,-r<PERSON>,/usr/lib/x86_64-linux-gnu/libz.so:/home/<USER>/Code/openappsec/build/core/compression", "role": "libraries"}, {"backtrace": 5, "fragment": "-Wl,--start-group", "role": "libraries"}, {"backtrace": 5, "fragment": "../libmessaging_buffer_comp.a", "role": "libraries"}, {"backtrace": 5, "fragment": "../../messaging_comp/libmessaging_comp.a", "role": "libraries"}, {"backtrace": 5, "fragment": "../../../metric/libmetric.a", "role": "libraries"}, {"backtrace": 5, "fragment": "../../../event_is/libevent_is.a", "role": "libraries"}, {"backtrace": 5, "fragment": "-lboost_regex", "role": "libraries"}, {"backtrace": 5, "fragment": "../../../shell_cmd/libshell_cmd.a", "role": "libraries"}, {"backtrace": 5, "fragment": "../../../time_proxy/libtime_proxy.a", "role": "libraries"}, {"backtrace": 5, "fragment": "../../../agent_details/libagent_details.a", "role": "libraries"}, {"backtrace": 5, "fragment": "../../../instance_awareness/libinstance_awareness.a", "role": "libraries"}, {"backtrace": 5, "fragment": "../../../version/libversion.a", "role": "libraries"}, {"backtrace": 5, "fragment": "../../../debug_is/libdebug_is.a", "role": "libraries"}, {"backtrace": 5, "fragment": "../../../report/libreport.a", "role": "libraries"}, {"backtrace": 5, "fragment": "../../../cptest/libcptest.a", "role": "libraries"}, {"backtrace": 5, "fragment": "-l<PERSON><PERSON><PERSON>", "role": "libraries"}, {"backtrace": 5, "fragment": "../../../../components/packet/libpacket.a", "role": "libraries"}, {"backtrace": 5, "fragment": "../../../singleton/libsingleton.a", "role": "libraries"}, {"backtrace": 5, "fragment": "../../../environment/libenvironment.a", "role": "libraries"}, {"backtrace": 5, "fragment": "../../../metric/libmetric.a", "role": "libraries"}, {"backtrace": 5, "fragment": "../../../event_is/libevent_is.a", "role": "libraries"}, {"backtrace": 5, "fragment": "../../../buffers/libbuffers.a", "role": "libraries"}, {"backtrace": 5, "fragment": "../../../rest/librest.a", "role": "libraries"}, {"backtrace": 5, "fragment": "../../../config/libconfig.a", "role": "libraries"}, {"backtrace": 5, "fragment": "../../../compression/libcompression_utils.so", "role": "libraries"}, {"backtrace": 5, "fragment": "-lz", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libgtest.a", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libgtest_main.a", "role": "libraries"}, {"backtrace": 5, "fragment": "-lgmock", "role": "libraries"}, {"backtrace": 5, "fragment": "-lboost_regex", "role": "libraries"}, {"backtrace": 5, "fragment": "-l<PERSON><PERSON><PERSON>", "role": "libraries"}, {"backtrace": 5, "fragment": "-ldl", "role": "libraries"}, {"backtrace": 5, "fragment": "-Wl,--end-group", "role": "libraries"}, {"backtrace": 5, "fragment": "-lz", "role": "libraries"}, {"backtrace": 5, "fragment": "-lgmock", "role": "libraries"}, {"backtrace": 5, "fragment": "-ldl", "role": "libraries"}, {"backtrace": 7, "fragment": "../../../agent_core_utilities/libagent_core_utilities.a", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libgtest.a", "role": "libraries"}], "language": "CXX"}, "name": "messaging_buffer_comp_ut", "nameOnDisk": "messaging_buffer_comp_ut", "paths": {"build": "core/messaging/messaging_buffer_comp/messaging_buffer_comp_ut", "source": "core/messaging/messaging_buffer_comp/messaging_buffer_comp_ut"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 2, "compileGroupIndex": 0, "path": "core/messaging/messaging_buffer_comp/messaging_buffer_comp_ut/messaging_buffer_comp_ut.cc", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}