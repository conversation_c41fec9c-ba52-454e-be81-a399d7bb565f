{"artifacts": [{"path": "components/utils/utilities/nginx_conf_collector/nginx_conf_collector_bin"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "install", "link_directories", "target_link_libraries", "target_compile_definitions", "include_directories"], "files": ["components/utils/utilities/nginx_conf_collector/CMakeLists.txt", "CMakeLists.txt", "core/messaging/CMakeLists.txt", "core/config/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 11, "parent": 0}, {"command": 1, "file": 0, "line": 36, "parent": 0}, {"file": 1}, {"command": 2, "file": 1, "line": 20, "parent": 3}, {"command": 2, "file": 0, "line": 7, "parent": 0}, {"command": 2, "file": 0, "line": 8, "parent": 0}, {"command": 3, "file": 0, "line": 14, "parent": 0}, {"file": 2}, {"command": 3, "file": 2, "line": 7, "parent": 8}, {"file": 3}, {"command": 3, "file": 3, "line": 2, "parent": 10}, {"command": 4, "file": 0, "line": 12, "parent": 0}, {"command": 5, "file": 1, "line": 21, "parent": 3}, {"command": 5, "file": 1, "line": 22, "parent": 3}, {"command": 5, "file": 1, "line": 26, "parent": 3}, {"command": 5, "file": 1, "line": 27, "parent": 3}, {"command": 5, "file": 1, "line": 28, "parent": 3}, {"command": 5, "file": 1, "line": 29, "parent": 3}, {"command": 5, "file": 1, "line": 30, "parent": 3}, {"command": 5, "file": 1, "line": 31, "parent": 3}, {"command": 5, "file": 1, "line": 32, "parent": 3}, {"command": 5, "file": 1, "line": 33, "parent": 3}, {"command": 5, "file": 1, "line": 34, "parent": 3}, {"command": 5, "file": 1, "line": 35, "parent": 3}, {"command": 5, "file": 1, "line": 36, "parent": 3}, {"command": 5, "file": 1, "line": 37, "parent": 3}, {"command": 5, "file": 0, "line": 1, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -O2 -fPIC -Wall -Wno-terminate -g"}], "defines": [{"backtrace": 12, "define": "NGINX_CONF_COLLECTOR_VERSION=\"\""}], "includes": [{"backtrace": 13, "path": "/usr/include/libxml2"}, {"backtrace": 14, "path": "/usr/src/googletest/googlemock/include"}, {"backtrace": 15, "path": "/home/<USER>/Code/openappsec/external"}, {"backtrace": 16, "path": "/home/<USER>/Code/openappsec/external/yajl/yajl-2.1.1/include"}, {"backtrace": 17, "path": "/home/<USER>/Code/openappsec/external/C-Mock/include/cmock"}, {"backtrace": 18, "path": "/home/<USER>/Code/openappsec/external/picojson"}, {"backtrace": 19, "path": "/home/<USER>/Code/openappsec/core/include/general"}, {"backtrace": 20, "path": "/home/<USER>/Code/openappsec/core/include/internal"}, {"backtrace": 21, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/interfaces"}, {"backtrace": 22, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/resources"}, {"backtrace": 23, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/utilities"}, {"backtrace": 24, "path": "/home/<USER>/Code/openappsec/core/include/attachments"}, {"backtrace": 25, "path": "/home/<USER>/Code/openappsec/events/include"}, {"backtrace": 26, "path": "/home/<USER>/Code/openappsec/components/include"}, {"backtrace": 27, "path": "/home/<USER>/Code/openappsec/core/include"}], "language": "CXX", "sourceIndexes": [0]}], "dependencies": [{"backtrace": 7, "id": "agent_core_utilities::@a13315131675cfa87ba6"}, {"backtrace": 7, "id": "shell_cmd::@11ee51f991ec943c1614"}, {"backtrace": 7, "id": "debug_is::@a0c1859d2372bc787301"}, {"backtrace": 7, "id": "time_proxy::@b3dbbbe1cfe4be1867c6"}, {"backtrace": 7, "id": "singleton::@1e8aa1f95e3638a33048"}, {"backtrace": 7, "id": "mainloop::@ba6dcd28fd73657d6d1b"}, {"backtrace": 7, "id": "environment::@42a05e874c90aabce0a2"}, {"backtrace": 7, "id": "rest::@552c489a81002f1765c1"}, {"backtrace": 7, "id": "report::@1270cc86e0390e7112d3"}, {"backtrace": 7, "id": "messaging::@ff33175e46fc4a6269a1"}, {"backtrace": 7, "id": "messaging_comp::@8afdec6b84cf90c070f7"}, {"backtrace": 7, "id": "connection::@0c7231ed830aec82add9"}, {"backtrace": 7, "id": "messaging_buffer_comp::@e95b015eba07c5bbf346"}, {"backtrace": 7, "id": "config::@d0a6a499a33ca3bb4e51"}, {"backtrace": 7, "id": "event_is::@262d1818dfec6bf12713"}, {"backtrace": 7, "id": "metric::@ead7061abeb3290a9386"}, {"backtrace": 7, "id": "version::@a1ad84cd100617c107a5"}, {"backtrace": 7, "id": "compression_utils::@3dcc85d11fba6ff84d38"}, {"backtrace": 7, "id": "nginx_utils::@43a0165d2cd6da678d6e"}], "id": "nginx_conf_collector_bin::@0ee53878e3e6a01045dc", "install": {"destinations": [{"backtrace": 2, "path": "bin"}], "prefix": {"path": "/usr/local"}}, "link": {"commandFragments": [{"fragment": "-O2 -fPIC -Wall -Wno-terminate -g", "role": "flags"}, {"fragment": "-rdynamic", "role": "flags"}, {"backtrace": 4, "fragment": "-L/usr/lib/x86_64-linux-gnu/libz.so", "role": "libraryPath"}, {"backtrace": 5, "fragment": "-L/home/<USER>/Code/openappsec/build/core", "role": "libraryPath"}, {"backtrace": 6, "fragment": "-L/home/<USER>/Code/openappsec/build/core/compression", "role": "libraryPath"}, {"fragment": "-Wl,-r<PERSON>,/usr/lib/x86_64-linux-gnu/libz.so:/home/<USER>/Code/openappsec/build/core:/home/<USER>/Code/openappsec/build/core/compression:", "role": "libraries"}, {"backtrace": 7, "fragment": "../../../../core/shell_cmd/libshell_cmd.a", "role": "libraries"}, {"backtrace": 7, "fragment": "../../../../core/mainloop/libmainloop.a", "role": "libraries"}, {"backtrace": 7, "fragment": "../../../../core/messaging/libmessaging.a", "role": "libraries"}, {"backtrace": 7, "fragment": "../../../../core/event_is/libevent_is.a", "role": "libraries"}, {"backtrace": 7, "fragment": "../../../../core/metric/libmetric.a", "role": "libraries"}, {"backtrace": 7, "fragment": "../../../../core/compression/libcompression_utils.so", "role": "libraries"}, {"backtrace": 7, "fragment": "-lz", "role": "libraries"}, {"backtrace": 7, "fragment": "../../nginx_utils/libnginx_utils.a", "role": "libraries"}, {"backtrace": 7, "fragment": "../../../../core/time_proxy/libtime_proxy.a", "role": "libraries"}, {"backtrace": 7, "fragment": "../../../../core/debug_is/libdebug_is.a", "role": "libraries"}, {"backtrace": 7, "fragment": "../../../../core/version/libversion.a", "role": "libraries"}, {"backtrace": 7, "fragment": "../../../../core/report/libreport.a", "role": "libraries"}, {"backtrace": 7, "fragment": "../../../../core/config/libconfig.a", "role": "libraries"}, {"backtrace": 7, "fragment": "../../../../core/environment/libenvironment.a", "role": "libraries"}, {"backtrace": 7, "fragment": "../../../../core/singleton/libsingleton.a", "role": "libraries"}, {"backtrace": 7, "fragment": "../../../../core/rest/librest.a", "role": "libraries"}, {"backtrace": 7, "fragment": "-lboost_context", "role": "libraries"}, {"backtrace": 7, "fragment": "-lboost_regex", "role": "libraries"}, {"backtrace": 7, "fragment": "-l<PERSON><PERSON><PERSON>", "role": "libraries"}, {"backtrace": 9, "fragment": "../../../../core/messaging/messaging_comp/libmessaging_comp.a", "role": "libraries"}, {"backtrace": 9, "fragment": "../../../../core/messaging/connection/libconnection.a", "role": "libraries"}, {"backtrace": 9, "fragment": "../../../../core/messaging/messaging_buffer_comp/libmessaging_buffer_comp.a", "role": "libraries"}, {"backtrace": 11, "fragment": "../../../../core/agent_core_utilities/libagent_core_utilities.a", "role": "libraries"}], "language": "CXX"}, "name": "nginx_conf_collector_bin", "nameOnDisk": "nginx_conf_collector_bin", "paths": {"build": "components/utils/utilities/nginx_conf_collector", "source": "components/utils/utilities/nginx_conf_collector"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "components/utils/utilities/nginx_conf_collector/nginx_conf_collector.cc", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}