{"archive": {}, "artifacts": [{"path": "components/utils/http_transaction_data/libhttp_transaction_data.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "add_definitions", "include_directories"], "files": ["components/utils/http_transaction_data/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 3, "parent": 0}, {"command": 1, "file": 0, "line": 1, "parent": 0}, {"file": 1}, {"command": 2, "file": 1, "line": 21, "parent": 3}, {"command": 2, "file": 1, "line": 22, "parent": 3}, {"command": 2, "file": 1, "line": 26, "parent": 3}, {"command": 2, "file": 1, "line": 27, "parent": 3}, {"command": 2, "file": 1, "line": 28, "parent": 3}, {"command": 2, "file": 1, "line": 29, "parent": 3}, {"command": 2, "file": 1, "line": 30, "parent": 3}, {"command": 2, "file": 1, "line": 31, "parent": 3}, {"command": 2, "file": 1, "line": 32, "parent": 3}, {"command": 2, "file": 1, "line": 33, "parent": 3}, {"command": 2, "file": 1, "line": 34, "parent": 3}, {"command": 2, "file": 1, "line": 35, "parent": 3}, {"command": 2, "file": 1, "line": 36, "parent": 3}, {"command": 2, "file": 1, "line": 37, "parent": 3}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -O2 -fPIC -Wall -Wno-terminate -g"}], "defines": [{"backtrace": 2, "define": "USERSPACE"}], "includes": [{"backtrace": 4, "path": "/usr/include/libxml2"}, {"backtrace": 5, "path": "/usr/src/googletest/googlemock/include"}, {"backtrace": 6, "path": "/home/<USER>/Code/openappsec/external"}, {"backtrace": 7, "path": "/home/<USER>/Code/openappsec/external/yajl/yajl-2.1.1/include"}, {"backtrace": 8, "path": "/home/<USER>/Code/openappsec/external/C-Mock/include/cmock"}, {"backtrace": 9, "path": "/home/<USER>/Code/openappsec/external/picojson"}, {"backtrace": 10, "path": "/home/<USER>/Code/openappsec/core/include/general"}, {"backtrace": 11, "path": "/home/<USER>/Code/openappsec/core/include/internal"}, {"backtrace": 12, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/interfaces"}, {"backtrace": 13, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/resources"}, {"backtrace": 14, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/utilities"}, {"backtrace": 15, "path": "/home/<USER>/Code/openappsec/core/include/attachments"}, {"backtrace": 16, "path": "/home/<USER>/Code/openappsec/events/include"}, {"backtrace": 17, "path": "/home/<USER>/Code/openappsec/components/include"}], "language": "CXX", "sourceIndexes": [0]}], "id": "http_transaction_data::@22977122975000546c97", "name": "http_transaction_data", "nameOnDisk": "libhttp_transaction_data.a", "paths": {"build": "components/utils/http_transaction_data", "source": "components/utils/http_transaction_data"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "components/utils/http_transaction_data/http_transaction_data.cc", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}