{"archive": {}, "artifacts": [{"path": "components/attachment-intakers/nginx_attachment/libnginx_attachment.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "add_definitions", "include_directories"], "files": ["components/attachment-intakers/nginx_attachment/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 3, "parent": 0}, {"command": 1, "file": 0, "line": 5, "parent": 0}, {"command": 2, "file": 0, "line": 1, "parent": 0}, {"file": 1}, {"command": 3, "file": 1, "line": 21, "parent": 4}, {"command": 3, "file": 1, "line": 22, "parent": 4}, {"command": 3, "file": 1, "line": 26, "parent": 4}, {"command": 3, "file": 1, "line": 27, "parent": 4}, {"command": 3, "file": 1, "line": 28, "parent": 4}, {"command": 3, "file": 1, "line": 29, "parent": 4}, {"command": 3, "file": 1, "line": 30, "parent": 4}, {"command": 3, "file": 1, "line": 31, "parent": 4}, {"command": 3, "file": 1, "line": 32, "parent": 4}, {"command": 3, "file": 1, "line": 33, "parent": 4}, {"command": 3, "file": 1, "line": 34, "parent": 4}, {"command": 3, "file": 1, "line": 35, "parent": 4}, {"command": 3, "file": 1, "line": 36, "parent": 4}, {"command": 3, "file": 1, "line": 37, "parent": 4}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -O2 -fPIC -Wall -Wno-terminate -g"}], "defines": [{"backtrace": 3, "define": "USERSPACE"}], "includes": [{"backtrace": 5, "path": "/usr/include/libxml2"}, {"backtrace": 6, "path": "/usr/src/googletest/googlemock/include"}, {"backtrace": 7, "path": "/home/<USER>/Code/openappsec/external"}, {"backtrace": 8, "path": "/home/<USER>/Code/openappsec/external/yajl/yajl-2.1.1/include"}, {"backtrace": 9, "path": "/home/<USER>/Code/openappsec/external/C-Mock/include/cmock"}, {"backtrace": 10, "path": "/home/<USER>/Code/openappsec/external/picojson"}, {"backtrace": 11, "path": "/home/<USER>/Code/openappsec/core/include/general"}, {"backtrace": 12, "path": "/home/<USER>/Code/openappsec/core/include/internal"}, {"backtrace": 13, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/interfaces"}, {"backtrace": 14, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/resources"}, {"backtrace": 15, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/utilities"}, {"backtrace": 16, "path": "/home/<USER>/Code/openappsec/core/include/attachments"}, {"backtrace": 17, "path": "/home/<USER>/Code/openappsec/events/include"}, {"backtrace": 18, "path": "/home/<USER>/Code/openappsec/components/include"}], "language": "CXX", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7]}], "dependencies": [{"backtrace": 2, "id": "buffers::@383b6442fe4f30043a47"}, {"backtrace": 2, "id": "table::@c4cf692f4b54175dff2b"}, {"backtrace": 2, "id": "connkey::@2b7274ba3c33850b0cf0"}, {"backtrace": 2, "id": "http_configuration::@93db0a132eac751b282e"}, {"backtrace": 2, "id": "http_transaction_data::@22977122975000546c97"}], "id": "nginx_attachment::@9a35884d437d9fc337f1", "name": "nginx_attachment", "nameOnDisk": "libnginx_attachment.a", "paths": {"build": "components/attachment-intakers/nginx_attachment", "source": "components/attachment-intakers/nginx_attachment"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "components/attachment-intakers/nginx_attachment/nginx_attachment.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/attachment-intakers/nginx_attachment/nginx_attachment_config.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/attachment-intakers/nginx_attachment/nginx_attachment_opaque.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/attachment-intakers/nginx_attachment/nginx_parser.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/attachment-intakers/nginx_attachment/user_identifiers_config.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/attachment-intakers/nginx_attachment/nginx_intaker_metric.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/attachment-intakers/nginx_attachment/nginx_attachment_metric.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "components/attachment-intakers/nginx_attachment/cidrs_data.cc", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}