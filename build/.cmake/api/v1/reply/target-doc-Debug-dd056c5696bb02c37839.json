{"backtrace": 3, "backtraceGraph": {"commands": ["ADD_CUSTOM_TARGET", "INCLUDE"], "files": ["external/yajl/YAJLDoc.cmake", "external/yajl/CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 73, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 16, "parent": 2}]}, "id": "doc::@537d9e5e9d983b588a46", "name": "doc", "paths": {"build": "external/yajl", "source": "external/yajl"}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1]}], "sources": [{"backtrace": 3, "isGenerated": true, "path": "build/external/yajl/CMakeFiles/doc", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/external/yajl/CMakeFiles/doc.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}