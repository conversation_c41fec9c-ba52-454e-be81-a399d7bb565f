{"artifacts": [{"path": "nodes/orchestration/orchestration_comp"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "install", "link_directories", "target_link_libraries", "add_dependencies", "include_directories"], "files": ["nodes/orchestration/CMakeLists.txt", "CMakeLists.txt", "nodes/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 3, "parent": 0}, {"command": 1, "file": 0, "line": 40, "parent": 0}, {"command": 1, "file": 0, "line": 41, "parent": 0}, {"file": 1}, {"command": 2, "file": 1, "line": 20, "parent": 4}, {"file": 2}, {"command": 2, "file": 2, "line": 6, "parent": 6}, {"command": 2, "file": 2, "line": 7, "parent": 6}, {"command": 3, "file": 0, "line": 5, "parent": 0}, {"command": 4, "file": 0, "line": 38, "parent": 0}, {"command": 5, "file": 1, "line": 21, "parent": 4}, {"command": 5, "file": 1, "line": 22, "parent": 4}, {"command": 5, "file": 1, "line": 26, "parent": 4}, {"command": 5, "file": 1, "line": 27, "parent": 4}, {"command": 5, "file": 1, "line": 28, "parent": 4}, {"command": 5, "file": 1, "line": 29, "parent": 4}, {"command": 5, "file": 1, "line": 30, "parent": 4}, {"command": 5, "file": 1, "line": 31, "parent": 4}, {"command": 5, "file": 1, "line": 32, "parent": 4}, {"command": 5, "file": 1, "line": 33, "parent": 4}, {"command": 5, "file": 1, "line": 34, "parent": 4}, {"command": 5, "file": 1, "line": 35, "parent": 4}, {"command": 5, "file": 1, "line": 36, "parent": 4}, {"command": 5, "file": 1, "line": 37, "parent": 4}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -O2 -fPIC -Wall -Wno-terminate -g"}], "includes": [{"backtrace": 11, "path": "/usr/include/libxml2"}, {"backtrace": 12, "path": "/usr/src/googletest/googlemock/include"}, {"backtrace": 13, "path": "/home/<USER>/Code/openappsec/external"}, {"backtrace": 14, "path": "/home/<USER>/Code/openappsec/external/yajl/yajl-2.1.1/include"}, {"backtrace": 15, "path": "/home/<USER>/Code/openappsec/external/C-Mock/include/cmock"}, {"backtrace": 16, "path": "/home/<USER>/Code/openappsec/external/picojson"}, {"backtrace": 17, "path": "/home/<USER>/Code/openappsec/core/include/general"}, {"backtrace": 18, "path": "/home/<USER>/Code/openappsec/core/include/internal"}, {"backtrace": 19, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/interfaces"}, {"backtrace": 20, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/resources"}, {"backtrace": 21, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/utilities"}, {"backtrace": 22, "path": "/home/<USER>/Code/openappsec/core/include/attachments"}, {"backtrace": 23, "path": "/home/<USER>/Code/openappsec/events/include"}, {"backtrace": 24, "path": "/home/<USER>/Code/openappsec/components/include"}], "language": "CXX", "sourceIndexes": [0]}], "dependencies": [{"backtrace": 10, "id": "ngen_core::@57760688d1f824db5d9c"}, {"backtrace": 9, "id": "version::@a1ad84cd100617c107a5"}, {"backtrace": 9, "id": "env_details::@3581fee6c10bd5aabdd0"}, {"backtrace": 9, "id": "signal_handler::@06657768f40ddbd8cb85"}, {"backtrace": 9, "id": "generic_rulebase::@bb67be512c8681bc130b"}, {"backtrace": 9, "id": "generic_rulebase_evaluators::@68906a8a6cd19d6f7ace"}, {"backtrace": 9, "id": "http_transaction_data::@22977122975000546c97"}, {"backtrace": 9, "id": "ip_utilities::@ccbabc50234818a45efb"}, {"backtrace": 9, "id": "service_health_status::@c6d677f23a792ff6ec31"}, {"backtrace": 9, "id": "local_policy_mgmt_gen::@0a40031ee6fe94db94bb"}, {"backtrace": 9, "id": "orchestration::@a9649a0f6745bd13b9da"}, {"backtrace": 9, "id": "orchestration_tools::@c0463e60b1cee0f04078"}, {"backtrace": 9, "id": "orchestration_modules::@35442e876a7612c6cbf9"}, {"backtrace": 9, "id": "orchestration_downloader::@b828530042f6dea30bc5"}, {"backtrace": 9, "id": "service_controller::@130725801dd33baf419c"}, {"backtrace": 9, "id": "package_handler::@0f8d720b8f19536f68b5"}, {"backtrace": 9, "id": "manifest_controller::@f3e0b97bd843ab7e4236"}, {"backtrace": 9, "id": "update_communication::@2e0ebfd087271db3e067"}, {"backtrace": 9, "id": "details_resolver::@2a494e899d7aacd006f3"}, {"backtrace": 9, "id": "health_check::@d7d6cb0f49ab201b9b8e"}, {"backtrace": 9, "id": "health_check_manager::@b1152599da57f556931c"}, {"backtrace": 9, "id": "updates_process_reporter::@aeae672129fa1cd73ab5"}, {"backtrace": 9, "id": "external_sdk_server::@3cfcf5481398102aad84"}], "id": "orchestration_comp::@b474fdd263f216d3bed2", "install": {"destinations": [{"backtrace": 2, "path": "bin"}, {"backtrace": 3, "path": "orchestration/bin"}], "prefix": {"path": "/usr/local"}}, "link": {"commandFragments": [{"fragment": "-O2 -fPIC -Wall -Wno-terminate -g", "role": "flags"}, {"fragment": "-rdynamic", "role": "flags"}, {"backtrace": 5, "fragment": "-L/usr/lib/x86_64-linux-gnu/libz.so", "role": "libraryPath"}, {"backtrace": 7, "fragment": "-L/home/<USER>/Code/openappsec/build/core", "role": "libraryPath"}, {"backtrace": 8, "fragment": "-L/home/<USER>/Code/openappsec/build/core/compression", "role": "libraryPath"}, {"fragment": "-Wl,-r<PERSON>,/usr/lib/x86_64-linux-gnu/libz.so:/home/<USER>/Code/openappsec/build/core:/home/<USER>/Code/openappsec/build/core/compression:", "role": "libraries"}, {"backtrace": 9, "fragment": "-Wl,--start-group", "role": "libraries"}, {"backtrace": 9, "fragment": "-lngen_core", "role": "libraries"}, {"backtrace": 9, "fragment": "-lcompression_utils", "role": "libraries"}, {"backtrace": 9, "fragment": "-lssl", "role": "libraries"}, {"backtrace": 9, "fragment": "-lcrypto", "role": "libraries"}, {"backtrace": 9, "fragment": "-lz", "role": "libraries"}, {"backtrace": 9, "fragment": "-lboost_context", "role": "libraries"}, {"backtrace": 9, "fragment": "-lboost_atomic", "role": "libraries"}, {"backtrace": 9, "fragment": "-lboost_regex", "role": "libraries"}, {"backtrace": 9, "fragment": "-lboost_filesystem", "role": "libraries"}, {"backtrace": 9, "fragment": "-lboost_system", "role": "libraries"}, {"backtrace": 9, "fragment": "-l<PERSON><PERSON><PERSON>", "role": "libraries"}, {"backtrace": 9, "fragment": "../../components/utils/generic_rulebase/libgeneric_rulebase.a", "role": "libraries"}, {"backtrace": 9, "fragment": "../../components/utils/generic_rulebase/evaluators/libgeneric_rulebase_evaluators.a", "role": "libraries"}, {"backtrace": 9, "fragment": "../../components/utils/ip_utilities/libip_utilities.a", "role": "libraries"}, {"backtrace": 9, "fragment": "../../components/utils/http_transaction_data/libhttp_transaction_data.a", "role": "libraries"}, {"backtrace": 9, "fragment": "../../core/version/libversion.a", "role": "libraries"}, {"backtrace": 9, "fragment": "../../components/signal_handler/libsignal_handler.a", "role": "libraries"}, {"backtrace": 9, "fragment": "../../components/security_apps/orchestration/liborchestration.a", "role": "libraries"}, {"backtrace": 9, "fragment": "../../components/security_apps/orchestration/health_check/libhealth_check.a", "role": "libraries"}, {"backtrace": 9, "fragment": "../../components/security_apps/orchestration/health_check_manager/libhealth_check_manager.a", "role": "libraries"}, {"backtrace": 9, "fragment": "../../components/security_apps/orchestration/updates_process_reporter/libupdates_process_reporter.a", "role": "libraries"}, {"backtrace": 9, "fragment": "../../components/security_apps/orchestration/service_controller/libservice_controller.a", "role": "libraries"}, {"backtrace": 9, "fragment": "../../components/security_apps/orchestration/manifest_controller/libmanifest_controller.a", "role": "libraries"}, {"backtrace": 9, "fragment": "../../components/security_apps/orchestration/package_handler/libpackage_handler.a", "role": "libraries"}, {"backtrace": 9, "fragment": "../../components/security_apps/orchestration/modules/liborchestration_modules.a", "role": "libraries"}, {"backtrace": 9, "fragment": "../../components/security_apps/orchestration/details_resolver/libdetails_resolver.a", "role": "libraries"}, {"backtrace": 9, "fragment": "../../components/security_apps/orchestration/downloader/liborchestration_downloader.a", "role": "libraries"}, {"backtrace": 9, "fragment": "../../components/security_apps/orchestration/update_communication/libupdate_communication.a", "role": "libraries"}, {"backtrace": 9, "fragment": "../../components/security_apps/orchestration/orchestration_tools/liborchestration_tools.a", "role": "libraries"}, {"backtrace": 9, "fragment": "../../core/env_details/libenv_details.a", "role": "libraries"}, {"backtrace": 9, "fragment": "../../components/security_apps/local_policy_mgmt_gen/liblocal_policy_mgmt_gen.a", "role": "libraries"}, {"backtrace": 9, "fragment": "-lcurl", "role": "libraries"}, {"backtrace": 9, "fragment": "../../components/security_apps/orchestration/external_sdk_server/libexternal_sdk_server.a", "role": "libraries"}, {"backtrace": 9, "fragment": "../../components/utils/service_health_status/libservice_health_status.a", "role": "libraries"}, {"backtrace": 9, "fragment": "-Wl,--end-group", "role": "libraries"}], "language": "CXX"}, "name": "orchestration_comp", "nameOnDisk": "orchestration_comp", "paths": {"build": "nodes/orchestration", "source": "nodes/orchestration"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "nodes/orchestration/main.cc", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}