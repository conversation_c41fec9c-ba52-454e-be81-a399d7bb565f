{"backtraceGraph": {"commands": ["install"], "files": ["nodes/orchestration/package/cpnano_debug/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 5, "parent": 0}, {"command": 0, "file": 0, "line": 6, "parent": 0}]}, "installers": [{"backtrace": 1, "component": "Unspecified", "destination": "bin", "paths": ["nodes/orchestration/package/cpnano_debug/cpnano_debug"], "targetId": "cpnano_debug::@9056e168d11840a40e72", "targetIndex": 22, "type": "target"}, {"backtrace": 1, "component": "Unspecified", "cxxModuleBmiTarget": {"id": "cpnano_debug::@9056e168d11840a40e72", "index": 22}, "destination": "bin", "type": "cxxModuleBmi"}, {"backtrace": 2, "component": "Unspecified", "destination": "orchestration", "paths": ["nodes/orchestration/package/cpnano_debug/cpnano_debug"], "targetId": "cpnano_debug::@9056e168d11840a40e72", "targetIndex": 22, "type": "target"}, {"backtrace": 2, "component": "Unspecified", "cxxModuleBmiTarget": {"id": "cpnano_debug::@9056e168d11840a40e72", "index": 22}, "destination": "orchestration", "type": "cxxModuleBmi"}], "paths": {"build": "nodes/orchestration/package/cpnano_debug", "source": "nodes/orchestration/package/cpnano_debug"}}