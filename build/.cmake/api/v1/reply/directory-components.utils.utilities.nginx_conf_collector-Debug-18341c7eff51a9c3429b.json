{"backtraceGraph": {"commands": ["install"], "files": ["components/utils/utilities/nginx_conf_collector/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 36, "parent": 0}, {"command": 0, "file": 0, "line": 37, "parent": 0}]}, "installers": [{"backtrace": 1, "component": "Unspecified", "destination": "bin", "paths": ["components/utils/utilities/nginx_conf_collector/nginx_conf_collector_bin"], "targetId": "nginx_conf_collector_bin::@0ee53878e3e6a01045dc", "targetIndex": 93, "type": "target"}, {"backtrace": 1, "component": "Unspecified", "cxxModuleBmiTarget": {"id": "nginx_conf_collector_bin::@0ee53878e3e6a01045dc", "index": 93}, "destination": "bin", "type": "cxxModuleBmi"}, {"backtrace": 2, "component": "Unspecified", "destination": "central_nginx_manager/bin", "paths": [{"from": "components/utils/utilities/nginx_conf_collector/nginx_conf_collector_bin", "to": "cp-nano-nginx-conf-collector"}], "type": "file"}], "paths": {"build": "components/utils/utilities/nginx_conf_collector", "source": "components/utils/utilities/nginx_conf_collector"}}