{"backtraceGraph": {"commands": ["INSTALL", "install"], "files": ["external/graphqlparser/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 112, "parent": 0}, {"command": 0, "file": 0, "line": 116, "parent": 0}, {"command": 1, "file": 0, "line": 136, "parent": 0}]}, "installers": [{"backtrace": 1, "component": "Unspecified", "destination": "include/graphqlparser", "paths": ["external/graphqlparser/c", "build/external/graphqlparser/c"], "type": "directory"}, {"backtrace": 2, "component": "Unspecified", "destination": "include/graphqlparser", "paths": ["build/external/graphqlparser/Ast.h", "external/graphqlparser/AstNode.h", "build/external/graphqlparser/AstVisitor.h", "external/graphqlparser/GraphQLParser.h", "external/graphqlparser/JsonVisitor.h", "build/external/graphqlparser/location.hh"], "type": "file"}, {"backtrace": 3, "component": "Unspecified", "destination": "/usr/local/lib/pkgconfig", "paths": ["build/external/graphqlparser/libgraphqlparser.pc"], "type": "file"}], "paths": {"build": "external/graphqlparser", "source": "external/graphqlparser"}}