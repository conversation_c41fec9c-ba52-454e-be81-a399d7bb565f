{"backtrace": 2, "backtraceGraph": {"commands": ["add_custom_target", "gen_package"], "files": ["nodes/packaging.cmake", "nodes/agent_cache/CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 3, "parent": 0}, {"command": 0, "file": 0, "line": 45, "parent": 1}]}, "id": "make_package_agent_cache::@1de715c10b8abf976a47", "name": "make_package_agent_cache", "paths": {"build": "nodes/agent_cache", "source": "nodes/agent_cache"}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1, 2]}], "sources": [{"backtrace": 2, "isGenerated": true, "path": "build/nodes/agent_cache/CMakeFiles/make_package_agent_cache", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/nodes/agent_cache/CMakeFiles/make_package_agent_cache.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "/usr/local/install-cp-nano-agent-cache.sh.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}