{"artifacts": [{"path": "nodes/central_nginx_manager/cp-nano-central-nginx-manager"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "install", "link_directories", "target_link_libraries", "add_dependencies", "include_directories"], "files": ["nodes/central_nginx_manager/CMakeLists.txt", "CMakeLists.txt", "nodes/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 3, "parent": 0}, {"command": 1, "file": 0, "line": 26, "parent": 0}, {"command": 1, "file": 0, "line": 27, "parent": 0}, {"file": 1}, {"command": 2, "file": 1, "line": 20, "parent": 4}, {"file": 2}, {"command": 2, "file": 2, "line": 6, "parent": 6}, {"command": 2, "file": 2, "line": 7, "parent": 6}, {"command": 3, "file": 0, "line": 5, "parent": 0}, {"command": 4, "file": 0, "line": 24, "parent": 0}, {"command": 5, "file": 1, "line": 21, "parent": 4}, {"command": 5, "file": 1, "line": 22, "parent": 4}, {"command": 5, "file": 1, "line": 26, "parent": 4}, {"command": 5, "file": 1, "line": 27, "parent": 4}, {"command": 5, "file": 1, "line": 28, "parent": 4}, {"command": 5, "file": 1, "line": 29, "parent": 4}, {"command": 5, "file": 1, "line": 30, "parent": 4}, {"command": 5, "file": 1, "line": 31, "parent": 4}, {"command": 5, "file": 1, "line": 32, "parent": 4}, {"command": 5, "file": 1, "line": 33, "parent": 4}, {"command": 5, "file": 1, "line": 34, "parent": 4}, {"command": 5, "file": 1, "line": 35, "parent": 4}, {"command": 5, "file": 1, "line": 36, "parent": 4}, {"command": 5, "file": 1, "line": 37, "parent": 4}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -O2 -fPIC -Wall -Wno-terminate -g"}], "includes": [{"backtrace": 11, "path": "/usr/include/libxml2"}, {"backtrace": 12, "path": "/usr/src/googletest/googlemock/include"}, {"backtrace": 13, "path": "/home/<USER>/Code/openappsec/external"}, {"backtrace": 14, "path": "/home/<USER>/Code/openappsec/external/yajl/yajl-2.1.1/include"}, {"backtrace": 15, "path": "/home/<USER>/Code/openappsec/external/C-Mock/include/cmock"}, {"backtrace": 16, "path": "/home/<USER>/Code/openappsec/external/picojson"}, {"backtrace": 17, "path": "/home/<USER>/Code/openappsec/core/include/general"}, {"backtrace": 18, "path": "/home/<USER>/Code/openappsec/core/include/internal"}, {"backtrace": 19, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/interfaces"}, {"backtrace": 20, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/resources"}, {"backtrace": 21, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/utilities"}, {"backtrace": 22, "path": "/home/<USER>/Code/openappsec/core/include/attachments"}, {"backtrace": 23, "path": "/home/<USER>/Code/openappsec/events/include"}, {"backtrace": 24, "path": "/home/<USER>/Code/openappsec/components/include"}], "language": "CXX", "sourceIndexes": [0]}], "dependencies": [{"backtrace": 10, "id": "ngen_core::@57760688d1f824db5d9c"}, {"backtrace": 9, "id": "version::@a1ad84cd100617c107a5"}, {"backtrace": 9, "id": "signal_handler::@06657768f40ddbd8cb85"}, {"backtrace": 9, "id": "generic_rulebase::@bb67be512c8681bc130b"}, {"backtrace": 9, "id": "generic_rulebase_evaluators::@68906a8a6cd19d6f7ace"}, {"backtrace": 9, "id": "http_transaction_data::@22977122975000546c97"}, {"backtrace": 9, "id": "ip_utilities::@ccbabc50234818a45efb"}, {"backtrace": 9, "id": "nginx_utils::@43a0165d2cd6da678d6e"}, {"backtrace": 9, "id": "rate_limit_comp::@ccc459f56a5cada2373c"}, {"backtrace": 9, "id": "rate_limit_config::@ccc459f56a5cada2373c"}, {"backtrace": 9, "id": "central_nginx_manager::@08af631d0b66b08ed4b1"}, {"backtrace": 9, "id": "nginx_message_reader::@37696d0d1544311739e1"}], "id": "cp-nano-central-nginx-manager::@2138b9ab414ffd11efe3", "install": {"destinations": [{"backtrace": 2, "path": "bin"}, {"backtrace": 3, "path": "central_nginx_manager/bin"}], "prefix": {"path": "/usr/local"}}, "link": {"commandFragments": [{"fragment": "-O2 -fPIC -Wall -Wno-terminate -g", "role": "flags"}, {"fragment": "-rdynamic", "role": "flags"}, {"backtrace": 5, "fragment": "-L/usr/lib/x86_64-linux-gnu/libz.so", "role": "libraryPath"}, {"backtrace": 7, "fragment": "-L/home/<USER>/Code/openappsec/build/core", "role": "libraryPath"}, {"backtrace": 8, "fragment": "-L/home/<USER>/Code/openappsec/build/core/compression", "role": "libraryPath"}, {"fragment": "-Wl,-r<PERSON>,/usr/lib/x86_64-linux-gnu/libz.so:/home/<USER>/Code/openappsec/build/core:/home/<USER>/Code/openappsec/build/core/compression:", "role": "libraries"}, {"backtrace": 9, "fragment": "-Wl,--start-group", "role": "libraries"}, {"backtrace": 9, "fragment": "-lngen_core", "role": "libraries"}, {"backtrace": 9, "fragment": "-lcompression_utils", "role": "libraries"}, {"backtrace": 9, "fragment": "-lssl", "role": "libraries"}, {"backtrace": 9, "fragment": "-lcrypto", "role": "libraries"}, {"backtrace": 9, "fragment": "-lz", "role": "libraries"}, {"backtrace": 9, "fragment": "-lboost_context", "role": "libraries"}, {"backtrace": 9, "fragment": "-lboost_atomic", "role": "libraries"}, {"backtrace": 9, "fragment": "-lboost_regex", "role": "libraries"}, {"backtrace": 9, "fragment": "-lboost_filesystem", "role": "libraries"}, {"backtrace": 9, "fragment": "-lboost_system", "role": "libraries"}, {"backtrace": 9, "fragment": "-l<PERSON><PERSON><PERSON>", "role": "libraries"}, {"backtrace": 9, "fragment": "../../components/utils/generic_rulebase/libgeneric_rulebase.a", "role": "libraries"}, {"backtrace": 9, "fragment": "../../components/utils/generic_rulebase/evaluators/libgeneric_rulebase_evaluators.a", "role": "libraries"}, {"backtrace": 9, "fragment": "../../components/utils/ip_utilities/libip_utilities.a", "role": "libraries"}, {"backtrace": 9, "fragment": "../../core/version/libversion.a", "role": "libraries"}, {"backtrace": 9, "fragment": "../../components/signal_handler/libsignal_handler.a", "role": "libraries"}, {"backtrace": 9, "fragment": "../../components/security_apps/central_nginx_manager/libcentral_nginx_manager.a", "role": "libraries"}, {"backtrace": 9, "fragment": "../../components/nginx_message_reader/libnginx_message_reader.a", "role": "libraries"}, {"backtrace": 9, "fragment": "../../components/security_apps/rate_limit/librate_limit_comp.a", "role": "libraries"}, {"backtrace": 9, "fragment": "../../components/security_apps/rate_limit/librate_limit_config.a", "role": "libraries"}, {"backtrace": 9, "fragment": "../../components/utils/nginx_utils/libnginx_utils.a", "role": "libraries"}, {"backtrace": 9, "fragment": "../../components/utils/http_transaction_data/libhttp_transaction_data.a", "role": "libraries"}, {"backtrace": 9, "fragment": "-Wl,--end-group", "role": "libraries"}], "language": "CXX"}, "name": "cp-nano-central-nginx-manager", "nameOnDisk": "cp-nano-central-nginx-manager", "paths": {"build": "nodes/central_nginx_manager", "source": "nodes/central_nginx_manager"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "nodes/central_nginx_manager/main.cc", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}