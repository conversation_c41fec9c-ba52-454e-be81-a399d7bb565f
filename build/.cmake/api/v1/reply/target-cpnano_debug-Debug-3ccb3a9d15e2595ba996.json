{"artifacts": [{"path": "nodes/orchestration/package/cpnano_debug/cpnano_debug"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "install", "link_directories", "include_directories"], "files": ["nodes/orchestration/package/cpnano_debug/CMakeLists.txt", "CMakeLists.txt", "nodes/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 3, "parent": 0}, {"command": 1, "file": 0, "line": 5, "parent": 0}, {"command": 1, "file": 0, "line": 6, "parent": 0}, {"file": 1}, {"command": 2, "file": 1, "line": 20, "parent": 4}, {"file": 2}, {"command": 2, "file": 2, "line": 6, "parent": 6}, {"command": 2, "file": 2, "line": 7, "parent": 6}, {"command": 3, "file": 1, "line": 21, "parent": 4}, {"command": 3, "file": 1, "line": 22, "parent": 4}, {"command": 3, "file": 1, "line": 26, "parent": 4}, {"command": 3, "file": 1, "line": 27, "parent": 4}, {"command": 3, "file": 1, "line": 28, "parent": 4}, {"command": 3, "file": 1, "line": 29, "parent": 4}, {"command": 3, "file": 1, "line": 30, "parent": 4}, {"command": 3, "file": 1, "line": 31, "parent": 4}, {"command": 3, "file": 1, "line": 32, "parent": 4}, {"command": 3, "file": 1, "line": 33, "parent": 4}, {"command": 3, "file": 1, "line": 34, "parent": 4}, {"command": 3, "file": 1, "line": 35, "parent": 4}, {"command": 3, "file": 1, "line": 36, "parent": 4}, {"command": 3, "file": 1, "line": 37, "parent": 4}, {"command": 3, "file": 0, "line": 1, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -O2 -fPIC -Wall -Wno-terminate -g"}], "includes": [{"backtrace": 9, "path": "/usr/include/libxml2"}, {"backtrace": 10, "path": "/usr/src/googletest/googlemock/include"}, {"backtrace": 11, "path": "/home/<USER>/Code/openappsec/external"}, {"backtrace": 12, "path": "/home/<USER>/Code/openappsec/external/yajl/yajl-2.1.1/include"}, {"backtrace": 13, "path": "/home/<USER>/Code/openappsec/external/C-Mock/include/cmock"}, {"backtrace": 14, "path": "/home/<USER>/Code/openappsec/external/picojson"}, {"backtrace": 15, "path": "/home/<USER>/Code/openappsec/core/include/general"}, {"backtrace": 16, "path": "/home/<USER>/Code/openappsec/core/include/internal"}, {"backtrace": 17, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/interfaces"}, {"backtrace": 18, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/resources"}, {"backtrace": 19, "path": "/home/<USER>/Code/openappsec/core/include/services_sdk/utilities"}, {"backtrace": 20, "path": "/home/<USER>/Code/openappsec/core/include/attachments"}, {"backtrace": 21, "path": "/home/<USER>/Code/openappsec/events/include"}, {"backtrace": 22, "path": "/home/<USER>/Code/openappsec/components/include"}, {"backtrace": 23, "path": "/home/<USER>/Code/openappsec/attachments/kernel_modules/core/include/common_is"}], "language": "CXX", "sourceIndexes": [0]}], "id": "cpnano_debug::@9056e168d11840a40e72", "install": {"destinations": [{"backtrace": 2, "path": "bin"}, {"backtrace": 3, "path": "orchestration"}], "prefix": {"path": "/usr/local"}}, "link": {"commandFragments": [{"fragment": "-O2 -fPIC -Wall -Wno-terminate -g", "role": "flags"}, {"fragment": "-rdynamic", "role": "flags"}, {"backtrace": 5, "fragment": "-L/usr/lib/x86_64-linux-gnu/libz.so", "role": "libraryPath"}, {"backtrace": 7, "fragment": "-L/home/<USER>/Code/openappsec/build/core", "role": "libraryPath"}, {"backtrace": 8, "fragment": "-L/home/<USER>/Code/openappsec/build/core/compression", "role": "libraryPath"}], "language": "CXX"}, "name": "cpnano_debug", "nameOnDisk": "cpnano_debug", "paths": {"build": "nodes/orchestration/package/cpnano_debug", "source": "nodes/orchestration/package/cpnano_debug"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "nodes/orchestration/package/cpnano_debug/cpnano_debug.cc", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}