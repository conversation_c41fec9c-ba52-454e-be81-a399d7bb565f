{"backtraceGraph": {"commands": ["install"], "files": ["nodes/attachment_registration_manager/package/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 1, "parent": 0}, {"command": 0, "file": 0, "line": 2, "parent": 0}, {"command": 0, "file": 0, "line": 3, "parent": 0}, {"command": 0, "file": 0, "line": 4, "parent": 0}]}, "installers": [{"backtrace": 1, "component": "Unspecified", "destination": "attachment_registration_manager_service", "paths": ["nodes/attachment_registration_manager/package/install-attachment-registration-manager.sh"], "type": "file"}, {"backtrace": 2, "component": "Unspecified", "destination": "attachment_registration_manager_service/conf", "paths": ["nodes/attachment_registration_manager/package/debug-conf.json"], "type": "file"}, {"backtrace": 3, "component": "Unspecified", "destination": "attachment_registration_manager_service/conf", "paths": ["nodes/attachment_registration_manager/package/service-conf.json"], "type": "file"}, {"backtrace": 4, "component": "Unspecified", "destination": "attachment_registration_manager_service/conf", "paths": ["nodes/attachment_registration_manager/package/cp-nano-attachment-registration-manager.cfg"], "type": "file"}], "paths": {"build": "nodes/attachment_registration_manager/package", "source": "nodes/attachment_registration_manager/package"}}