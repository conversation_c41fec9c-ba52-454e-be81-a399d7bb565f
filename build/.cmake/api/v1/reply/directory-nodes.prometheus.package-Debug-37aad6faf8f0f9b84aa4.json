{"backtraceGraph": {"commands": ["install"], "files": ["nodes/prometheus/package/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 1, "parent": 0}, {"command": 0, "file": 0, "line": 2, "parent": 0}, {"command": 0, "file": 0, "line": 3, "parent": 0}, {"command": 0, "file": 0, "line": 4, "parent": 0}]}, "installers": [{"backtrace": 1, "component": "Unspecified", "destination": "prometheus_service", "paths": ["nodes/prometheus/package/install-cp-nano-prometheus.sh"], "type": "file"}, {"backtrace": 2, "component": "Unspecified", "destination": "prometheus_service/conf", "paths": ["nodes/prometheus/package/cp-nano-prometheus.cfg"], "type": "file"}, {"backtrace": 3, "component": "Unspecified", "destination": "prometheus_service/conf", "paths": ["nodes/prometheus/package/cp-nano-prometheus-conf.json"], "type": "file"}, {"backtrace": 4, "component": "Unspecified", "destination": "prometheus_service/conf", "paths": ["nodes/prometheus/package/cp-nano-prometheus-debug-conf.json"], "type": "file"}], "paths": {"build": "nodes/prometheus/package", "source": "nodes/prometheus/package"}}