<?xml version="1.0" encoding="UTF-8"?>
<Site BuildName="(empty)"
	BuildStamp="20250705-1712-Experimental"
	Name="(empty)"
	Generator="ctest-3.28.3"
	CompilerName=""
	CompilerVersion=""
	OSName="Linux"
	Hostname="L-AI-3"
	OSRelease="6.11.0-29-generic"
	OSVersion="#29~24.04.1-Ubuntu SMP PREEMPT_DYNAMIC Thu Jun 26 14:16:59 UTC 2"
	OSPlatform="x86_64"
	Is64Bits="1"
	VendorString="GenuineIntel"
	VendorID="Intel Corporation"
	FamilyID="6"
	ModelID="165"
	ProcessorCacheSize="16384"
	NumberOfLogicalCPU="16"
	NumberOfPhysicalCPU="8"
	TotalVirtualMemory="8191"
	TotalPhysicalMemory="63920"
	LogicalProcessorsPerPhysical="2"
	ProcessorClockFrequency="2600.02"
	>
	<Testing>
		<StartDateTime>Jul 05 12:12 CDT</StartDateTime>
		<StartTestTime>1751735563</StartTestTime>
		<TestList/>
		<EndDateTime>Jul 05 12:12 CDT</EndDateTime>
		<EndTestTime>1751735563</EndTestTime>
		<ElapsedMinutes>0</ElapsedMinutes>
	</Testing>
</Site>
